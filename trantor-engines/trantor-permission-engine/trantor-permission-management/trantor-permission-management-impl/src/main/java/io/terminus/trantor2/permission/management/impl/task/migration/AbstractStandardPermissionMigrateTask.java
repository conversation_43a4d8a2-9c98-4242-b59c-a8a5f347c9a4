package io.terminus.trantor2.permission.management.impl.task.migration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.utils.StandardPermissionKeyHelper;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.index.MetaIndexAsset;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.StepHelper;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.module.util.MenuUtils;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import io.terminus.trantor2.permission.management.api.service.PermissionKeyInitializer;
import io.terminus.trantor2.permission.management.impl.util.ViewJsonExtractor;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.repo.ViewRepo;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.common.meta.ServiceMeta;
import io.terminus.trantor2.service.common.meta.ServiceProps;
import io.terminus.trantor2.service.dsl.CascadeCreateDataNode;
import io.terminus.trantor2.service.dsl.CascadeDeleteDataNode;
import io.terminus.trantor2.service.dsl.CascadeFindDataNode;
import io.terminus.trantor2.service.dsl.CascadeUpdateDataNode;
import io.terminus.trantor2.service.dsl.CreateDataNode;
import io.terminus.trantor2.service.dsl.DataProcessingNode;
import io.terminus.trantor2.service.dsl.DeleteDataNode;
import io.terminus.trantor2.service.dsl.EndNode;
import io.terminus.trantor2.service.dsl.FindTreeDataNode;
import io.terminus.trantor2.service.dsl.MultiCreateDataNode;
import io.terminus.trantor2.service.dsl.MultiRetrieveDataNode;
import io.terminus.trantor2.service.dsl.PagingDataNode;
import io.terminus.trantor2.service.dsl.RetrieveDataNode;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.StartNode;
import io.terminus.trantor2.service.dsl.UpdateDataNode;
import io.terminus.trantor2.service.dsl.properties.DataProcessingProperties;
import io.terminus.trantor2.service.dsl.properties.ServiceProperties;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import io.terminus.trantor2.task.TaskOutput;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/7/2 16:14
 **/
@RequiredArgsConstructor
public abstract class AbstractStandardPermissionMigrateTask<O extends BaseTask.Options> extends BaseTask<O> {

    protected final MetaIndexAssetRepo metaIndexAssetRepo;
    protected final ViewRepo viewRepo;
    protected final ServiceRepo serviceRepo;
    protected final MenuConsoleQueryService menuConsoleQueryService;
    protected final PermissionKeyInitializer permissionKeyInitializer;

    /**
     * 是否执行迁移，true：执行，false：不执行
     */
    protected static boolean migrateOrNot;

    protected Collection<Result> handleService(TaskOutput output, TaskContext ctx, String moduleKey) {
        ResourceContext resourceCtx = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());
        Collection<Result> results = new ArrayList<>();
        Set<String> serviceKeys = metaIndexAssetRepo.find(ctx.getTeamId(),
                        MetaIndexAsset.Key.class,
                        Field.type().equal(MetaType.ServiceDefinition.name()).and(Field.moduleKey().equal(moduleKey)))
                .stream().map(MetaIndexAsset.Key::getKey).collect(Collectors.toSet());
        StepHelper stepHelper = StepHelper.newHelper(output);
        for (String serviceKey : serviceKeys) {
            stepHelper.exec(serviceKey, resultDetail -> {
                serviceRepo.findOneByKey(serviceKey, resourceCtx).ifPresent(serviceMeta -> {
                    results.addAll(handleSingleService(serviceMeta));
                    if (migrateOrNot) {
                        serviceRepo.update(serviceMeta, resourceCtx);
                    }
                });
            });
        }
        stepHelper.complete();
        return results;
    }

    public static Collection<Result> handleSingleService(@NotNull ServiceMeta serviceMeta) {
        List<Result> results = new ArrayList<>();
        String serviceKey = serviceMeta.getKey();
        ServiceProps serviceProps = serviceMeta.getResourceProps();
        ServiceType serviceType = serviceProps.getServiceType();
        ServiceDefinition serviceDslJson = serviceProps.getServiceDslJson();
        ServiceProperties props = serviceDslJson.getProps();
        if (ServiceType.CODE_RULE.equals(serviceType)) {
            String permissionKey = props.getPermissionKey();
            results.add(new Result(
                    ServiceType.CODE_RULE.name(),
                    serviceKey,
                    serviceMeta.getName(),
                    permissionKey,
                    permissionKey
            ));
        } else if (ServiceType.EVENT.equals(serviceType)) {
            String permissionKey = props.getPermissionKey();
            results.add(new Result(
                    ServiceType.EVENT.name(),
                    serviceKey,
                    serviceMeta.getName(),
                    permissionKey,
                    permissionKey
            ));
        } else if (ServiceType.SPI.equals(serviceType)) {
            String permissionKey = props.getPermissionKey();
            results.add(new Result(
                    ServiceType.SPI.name(),
                    serviceKey,
                    serviceMeta.getName(),
                    permissionKey,
                    permissionKey
            ));
        } else if (ServiceType.PROGRAMMABLE.equals(serviceType)) {
            results.addAll(handleSingleProgrammableService(serviceDslJson));
        } else if (ServiceType.SYSTEM.equals(serviceType)) {
            if (MapUtils.isNotEmpty(serviceProps.getPermissions())) {
                serviceProps.getPermissions().forEach((modelKey, permissionDTO) -> {
                    String newPermKey = StandardPermissionKeyHelper.SystemServiceKey.getPermissionKey(serviceKey, modelKey);
                    results.add(new Result(
                            ACLResourceType.SystemService.name(),
                            serviceKey,
                            serviceMeta.getName(),
                            permissionDTO.getFunctionPermissionKey(),
                            newPermKey
                    ));
                    if (migrateOrNot) {
                        permissionDTO.setFunctionPermissionKey(newPermKey);
                    }
                });
            }
        }
        return results;
    }

    public static List<Result> handleSingleProgrammableService(ServiceDefinition serviceDslJson) {
        List<Result> results = new ArrayList<>();
        String serviceKey = serviceDslJson.getKey();
        String serviceName = serviceDslJson.getName();
        ServiceProperties props = serviceDslJson.getProps();
        String permissionKey = props.getPermissionKey();
        if (Objects.nonNull(permissionKey)) {
            String newPermKey = permissionKey;
            if (StandardPermissionKeyHelper.ServiceKey.isStandard(serviceKey)) {
                String standardPermKey = StandardPermissionKeyHelper.ServiceKey.getPermissionKey(serviceKey);
                if (Objects.nonNull(standardPermKey)) {
                    newPermKey = standardPermKey;
                }
            } else {
                String stdPermKey;
                if (Objects.nonNull(stdPermKey = PermKeyReplacer.convertToStandardPermissionKey(permissionKey))
                        || Objects.nonNull(stdPermKey = inferStandardPermissionKey(serviceDslJson))) {
                    newPermKey = stdPermKey;
                }
            }
            results.add(new Result(
                    ACLResourceType.Service.name(),
                    serviceKey,
                    serviceName,
                    permissionKey,
                    newPermKey
            ));
            if (migrateOrNot) {
                props.setPermissionKey(newPermKey);
            }
        }
        return results;
    }

    private static String inferStandardPermissionKey(@NotNull ServiceDefinition serviceDefinition) {
        if (CollectionUtils.isEmpty(serviceDefinition.getChildren())) {
            return null;
        }
        List<ServiceElement<?>> filterServiceElements = serviceDefinition.getChildren().stream()
                .filter(se -> !(se instanceof StartNode || se instanceof EndNode))
                .collect(Collectors.toList());
        if (filterServiceElements.size() == 1 && filterServiceElements.get(0) instanceof DataProcessingNode) {
            ServiceElement<?> se = filterServiceElements.get(0);
            DataProcessingProperties props = (DataProcessingProperties) se.getProps();
            String modelKey = props.getModelKey();
            if (StringUtils.isBlank(modelKey)) {
                return null;
            }
            if (se instanceof CreateDataNode || se instanceof CascadeCreateDataNode || se instanceof MultiCreateDataNode) {
                return StandardPermissionKeyHelper.Operate.CREATE.getPermissionKey(modelKey);
            } else if (se instanceof DeleteDataNode || se instanceof CascadeDeleteDataNode) {
                return StandardPermissionKeyHelper.Operate.DELETE.getPermissionKey(modelKey);
            } else if (se instanceof UpdateDataNode || se instanceof CascadeUpdateDataNode) {
                return StandardPermissionKeyHelper.Operate.MODIFY.getPermissionKey(modelKey);
            } else if (se instanceof FindTreeDataNode || se instanceof CascadeFindDataNode || se instanceof PagingDataNode
                    || se instanceof RetrieveDataNode || se instanceof MultiRetrieveDataNode) {
                return StandardPermissionKeyHelper.Operate.DATA_QUERY.getPermissionKey(modelKey);
            }
        }
        return null;
    }

    protected Collection<Result> handleView(TaskOutput output, TaskContext ctx, String moduleKey) {
        ResourceContext resourceCtx = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());
        Collection<Result> results = new ArrayList<>();
        Set<String> viewKeys = metaIndexAssetRepo.find(ctx.getTeamId(),
                        MetaIndexAsset.Key.class,
                        Field.type().equal(MetaType.View.name()).and(Field.moduleKey().equal(moduleKey)))
                .stream().map(MetaIndexAsset.Key::getKey).collect(Collectors.toSet());
        StepHelper stepHelper = StepHelper.newHelper(output);
        for (String viewKey : viewKeys) {
            stepHelper.exec(viewKey, resultDetail -> {
                Optional<DataManagerViewMeta> viewMeta = viewRepo.findOneByKey(viewKey, resourceCtx);
                viewMeta.ifPresent(dataManagerViewMeta -> {
                    results.addAll(handleSingleView(dataManagerViewMeta));
                    if (migrateOrNot) {
                        viewRepo.update(dataManagerViewMeta, resourceCtx);
                    }
                });
            });
        }
        stepHelper.complete();
        return results;
    }

    public Collection<Result> handleSingleView(@NotNull DataManagerViewMeta dataManagerViewMeta) {
        DataManagerView dataManagerView = dataManagerViewMeta.getResourceProps();
        return handleSingleView(dataManagerView);
    }

    public Collection<Result> handleSingleView(@NotNull DataManagerView dataManagerView) {
        Collection<Result> results = new ArrayList<>();
        ArrayNode children = (ArrayNode) dataManagerView.getContent().get("children");
        Result viewResult = new Result(
                ACLResourceType.View.name(),
                dataManagerView.getKey(),
                dataManagerView.getTitle(),
                dataManagerView.getPermissionKey(),
                dataManagerView.getPermissionKey()
        );
        if (!children.isEmpty()) {
            String mainModelKey = ViewJsonExtractor.ViewContent.getViewMainModelKey(dataManagerView.getContent());
            Map<String, String> buttonKeyToModelKey = ViewJsonExtractor.ViewContent.getButtonKeyToModelKey(dataManagerView.getContent());
            if (Objects.nonNull(mainModelKey)) {
                if (DataManagerView.ViewType.LIST.equals(dataManagerView.getType())
                        || DataManagerView.ViewType.DETAIL.equals(dataManagerView.getType())) {
                    viewResult.setNewPermKey(StandardPermissionKeyHelper.Operate.VIEW.getPermissionKey(mainModelKey));
                    viewResult.setChildren(handleButton(
                                    dataManagerView.getContent(),
                                    btnKey -> buttonKeyToModelKey.getOrDefault(btnKey, mainModelKey),
                                    viewResult::getNewPermKey
                            )
                    );
                } else if (DataManagerView.ViewType.FORM.equals(dataManagerView.getType())) {
                    viewResult.setNewPermKey(StandardPermissionKeyHelper.Operate.MODIFY.getPermissionKey(mainModelKey));
                    viewResult.setChildren(handleButton(
                                    dataManagerView.getContent(),
                                    btnKey -> buttonKeyToModelKey.getOrDefault(btnKey, mainModelKey),
                                    viewResult::getNewPermKey
                            )
                    );
                } else {
                    viewResult.setNewPermKey(permissionKeyInitializer.createSemanticPermission(mainModelKey, dataManagerView.getKey(), dataManagerView.getTitle(), dataManagerView.getKey()));
                    viewResult.setChildren(handleButton(
                                    dataManagerView.getContent(),
                                    btnKey -> buttonKeyToModelKey.getOrDefault(btnKey, mainModelKey),
                                    viewResult::getNewPermKey
                            )
                    );
                }
                if (migrateOrNot) {
                    dataManagerView.setPermissionKey(viewResult.getNewPermKey());
                }
            }
        }
        results.add(viewResult);
        return results;
    }

    private Collection<Result> handleButton(@NotNull JsonNode viewContent,
                                            @NotNull Function<String, String> modelKeyLoader,
                                            @NotNull Supplier<String> viewPermKeyLoader) {
        Collection<Result> results = new ArrayList<>();
        List<JsonNode> parents = viewContent.findParents("name");
        for (JsonNode parent : parents) {
            String key = parent.path("key").textValue();
            String name = parent.path("name").textValue();
            if ("Button".equalsIgnoreCase(name)) {  // 常规按钮
                results.add(handleGenericButton(parent, modelKeyLoader, viewPermKeyLoader));
            } else if ("DropdownButton".equalsIgnoreCase(name)) {   // 按钮组
                results.addAll(handleDropdownButton(parent, modelKeyLoader, viewPermKeyLoader));
            } else if ("ImportButton".equalsIgnoreCase(name)) { // 导入按钮
                JsonNode props = parent.get("props");
                String importModelKey = ViewJsonExtractor.getModelKeyOrDefault(props, modelKeyLoader.apply(key));
                String label = props.path("label").textValue();
                String permissionKey = props.path("permissionKey").textValue();
                String newPermKey = StandardPermissionKeyHelper.Operate.IMPORT.getPermissionKey(importModelKey);
                results.add(new Result(
                        ACLResourceType.Button.name(),
                        key,
                        label,
                        permissionKey,
                        newPermKey
                ));
                if (migrateOrNot) {
                    ((ObjectNode) props).put("permissionKey", newPermKey);
                    ((ObjectNode) props).put("permissionEnabled", Boolean.TRUE);
                }
            } else if ("ExportButton".equalsIgnoreCase(name)) { // 导出按钮
                JsonNode props = parent.get("props");
                String exportModelKey = ViewJsonExtractor.getModelKeyOrDefault(props, modelKeyLoader.apply(key));
                String label = props.path("label").textValue();
                String permissionKey = props.path("permissionKey").textValue();
                String newPermKey = StandardPermissionKeyHelper.Operate.EXPORT.getPermissionKey(exportModelKey);
                results.add(new Result(
                        ACLResourceType.Button.name(),
                        key,
                        label,
                        permissionKey,
                        newPermKey
                ));
                if (migrateOrNot) {
                    ((ObjectNode) props).put("permissionKey", newPermKey);
                    ((ObjectNode) props).put("permissionEnabled", Boolean.TRUE);
                }
            } else if ("Print".equalsIgnoreCase(name)) { // 打印按钮
                JsonNode props = parent.get("props");
                String printModelKey = ViewJsonExtractor.getModelKeyOrDefault(props, modelKeyLoader.apply(key));
                String label = props.path("label").textValue();
                String permissionKey = props.path("permissionKey").textValue();
                String newPermKey = StandardPermissionKeyHelper.Operate.PRINT.getPermissionKey(printModelKey);
                results.add(new Result(
                        ACLResourceType.Button.name(),
                        key,
                        label,
                        permissionKey,
                        newPermKey
                ));
                if (migrateOrNot) {
                    ((ObjectNode) props).put("permissionKey", newPermKey);
                    ((ObjectNode) props).put("permissionEnabled", Boolean.TRUE);
                }
            } else if ("ApprovalInstanceBtn".equalsIgnoreCase(name)) {  // 审批记录按钮
                JsonNode props = parent.get("props");
                String approvalModelKey = ViewJsonExtractor.getModelKeyOrDefault(props, modelKeyLoader.apply(key));
                String label = props.path("label").textValue();
                String permissionKey = props.path("permissionKey").textValue();
                String newPermKey = StandardPermissionKeyHelper.Operate.APPROVAL.getPermissionKey(approvalModelKey);
                results.add(new Result(
                        ACLResourceType.Button.name(),
                        key,
                        label,
                        permissionKey,
                        newPermKey
                ));
                if (migrateOrNot) {
                    ((ObjectNode) props).put("permissionEnabled", Boolean.TRUE);
                    ((ObjectNode) props.path("ApprovalRecordBtnCancelServiceProps")).put("permissionKey", newPermKey);
                    ((ObjectNode) props.path("ApprovalRecordBtnUrgeServiceProps")).put("permissionKey", newPermKey);
                }
            }
        }
        return results;
    }

    private Result handleGenericButton(@NotNull JsonNode buttonJsonNode,
                                       @NotNull Function<String, String> modelKeyLoader,
                                       @NotNull Supplier<String> viewPermKeyLoader) {
        String key = buttonJsonNode.path("key").textValue();
        JsonNode props = buttonJsonNode.get("props");
        String label = props.path("label").textValue();
        String permissionKey = props.path("permissionKey").textValue();
        Result result;
        if (ViewJsonExtractor.Button.isBindServiceOrOpenView(props)) {
            String buttonModelKey = modelKeyLoader.apply(key);
            String btnPermKey = ViewJsonExtractor.Button.getStdPermKeyByBtnKeyOrLabel(buttonModelKey, key, label);
            if (Objects.isNull(btnPermKey)
                    && Objects.isNull(btnPermKey = permissionKeyInitializer.createSemanticPermission(buttonModelKey, key, label, buttonModelKey))) {
                btnPermKey = viewPermKeyLoader.get();
            }
            result = new Result(
                    ACLResourceType.Button.name(),
                    key,
                    label,
                    permissionKey,
                    btnPermKey
            );
            if (migrateOrNot) {
                ((ObjectNode) props).put("permissionKey", btnPermKey);
                ((ObjectNode) props).put("permissionEnabled", Boolean.TRUE);
            }
        } else {
            result = new Result(
                    ACLResourceType.Button.name(),
                    key,
                    label,
                    permissionKey,
                    null
            );
            if (migrateOrNot) {
                ((ObjectNode) props).remove("permissionKey");
                ((ObjectNode) props).put("permissionEnabled", Boolean.FALSE);
            }
        }
        return result;
    }

    private List<Result> handleDropdownButton(@NotNull JsonNode dropdownButton,
                                              @NotNull Function<String, String> modelKeyLoader,
                                              @NotNull Supplier<String> viewPermKeyLoader) {
        List<Result> results = new ArrayList<>();
        JsonNode propsItems = dropdownButton.at("/props/items");
        if (propsItems.isArray() && !propsItems.isEmpty()) {
            propsItems.forEach(item -> results.add(handleDropdownButtonItem(item, modelKeyLoader, viewPermKeyLoader)));
        }
        return results;
    }

    // 历史数据中的部分按钮组结构与传统Button结构存在差异，需要特殊处理
    private Result handleDropdownButtonItem(@NotNull JsonNode dropdownButtonItem,
                                            @NotNull Function<String, String> modelKeyLoader,
                                            @NotNull Supplier<String> viewPermKeyLoader) {
        String key = dropdownButtonItem.path("key").textValue();
        String label = dropdownButtonItem.path("label").textValue();
        String permissionKey = dropdownButtonItem.path("permissionKey").textValue();
        Result result;
        if (ViewJsonExtractor.Button.isBindServiceOrOpenView(dropdownButtonItem)) {
            String buttonModelKey = modelKeyLoader.apply(key);
            String btnPermKey = ViewJsonExtractor.Button.getStdPermKeyByBtnKeyOrLabel(buttonModelKey, key, label);
            if (Objects.isNull(btnPermKey)
                    && Objects.isNull(btnPermKey = permissionKeyInitializer.createSemanticPermission(buttonModelKey, key, label, buttonModelKey))) {
                btnPermKey = Objects.nonNull(permissionKey) ? permissionKey : viewPermKeyLoader.get();
            }
            result = new Result(
                    ACLResourceType.Button.name(),
                    key,
                    label,
                    permissionKey,
                    btnPermKey
            );
            if (migrateOrNot) {
                ((ObjectNode) dropdownButtonItem).put("permissionKey", btnPermKey);
                ((ObjectNode) dropdownButtonItem).put("permissionEnabled", Boolean.TRUE);
            }
        } else {
            result = new Result(
                    ACLResourceType.Button.name(),
                    key,
                    label,
                    permissionKey,
                    null
            );
            if (migrateOrNot) {
                ((ObjectNode) dropdownButtonItem).remove("permissionKey");
                ((ObjectNode) dropdownButtonItem).put("permissionEnabled", Boolean.FALSE);
            }
        }
        return result;
    }

    protected Collection<Result> handleMenu(TaskOutput output, String portalCode) {
        List<MenuMeta> menuMetaList = menuConsoleQueryService.getMenuTree(portalCode);
        List<MenuMeta> flatMenuMetaList = new ArrayList<>();
        MenuUtils.flatMenuMetaListRecursively(menuMetaList, flatMenuMetaList);
        return handleMenu(output, flatMenuMetaList);
    }

    public static Collection<Result> handleMenu(TaskOutput output,
                                                @NotNull Collection<MenuMeta> menuMetas) {
        List<Result> results = new ArrayList<>();
        StepHelper stepHelper = StepHelper.newHelper(output);
        for (MenuMeta menuMeta : menuMetas) {
            stepHelper.exec(menuMeta.getKey(), resultDetail -> {
                if (menuMeta.hasRoute()) {
                    results.add(
                            new Result(ACLResourceType.Menu.name(),
                                    menuMeta.getKey(),
                                    menuMeta.getLabel(),
                                    menuMeta.getPermissionKey(),
                                    menuMeta.getPermissionKey())
                    );
                }
            });
        }
        stepHelper.complete();
        return results;
    }

    @Getter
    @RequiredArgsConstructor
    enum PermKeyReplacer {
        CREATE_PERMISSION("^(.+?)_CREATE_PERMISSION", "$1:CREATE_PERMISSION", Pattern.compile("^(.+?)_CREATE_PERMISSION")),
        DELETE_PERMISSION("^(.+?)_DELETE_PERMISSION", "$1:DELETE_PERMISSION", Pattern.compile("^(.+?)_DELETE_PERMISSION")),
        MODIFY_PERMISSION("^(.+?)_UPDATE_PERMISSION", "$1:MODIFY_PERMISSION", Pattern.compile("^(.+?)_UPDATE_PERMISSION")),
        DATA_QUERY_PERMISSION("^(.+?)_RETRIEVE_PERMISSION", "$1:DATA_QUERY_PERMISSION", Pattern.compile("^(.+?)_RETRIEVE_PERMISSION"));

        private final String regex;
        private final String replacement;
        private final Pattern pattern;

        public static String convertToStandardPermissionKey(@NotNull String permissionKey) {
            for (PermKeyReplacer replacer : PermKeyReplacer.values()) {
                if (replacer.getPattern().matcher(permissionKey).matches()) {
                    return permissionKey.replaceFirst(replacer.getRegex(), replacer.getReplacement());
                }
            }
            return null;
        }
    }

    @Data
    public static class Result {
        private String resourceType;
        private String resourceKey;
        private String resourceLabel;
        private String oldPermKey;
        private String newPermKey;
        private Collection<Result> children;

        public Result(String resourceType, String resourceKey, String resourceLabel, String oldPermKey, String newPermKey) {
            this.resourceType = resourceType;
            this.resourceKey = resourceKey;
            this.resourceLabel = resourceLabel;
            this.oldPermKey = oldPermKey;
            this.newPermKey = newPermKey;
        }

        public boolean isRemovePermKeyOps() {
            return StringUtils.isNotBlank(this.oldPermKey) && StringUtils.isBlank(this.newPermKey);
        }
    }
}
