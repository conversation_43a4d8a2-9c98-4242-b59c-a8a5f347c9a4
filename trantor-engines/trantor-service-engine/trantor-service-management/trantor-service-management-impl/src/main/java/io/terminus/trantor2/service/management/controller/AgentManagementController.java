package io.terminus.trantor2.service.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.common.api.util.JsonUtils;
import io.terminus.common.scheduler.enums.JobTypeEnum;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.common.meta.AIAgentMeta;
import io.terminus.trantor2.service.dsl.Agent;
import io.terminus.trantor2.service.dsl.enums.AgentTriggerType;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.BaseField;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.HttpTool;
import io.terminus.trantor2.service.dsl.properties.RelatedModel;
import io.terminus.trantor2.service.dsl.properties.SchedulerJob;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.*;
import io.terminus.trantor2.service.dsl.properties.*;
import io.terminus.trantor2.service.dsl.properties.trigger.AgentEventTrigger;
import io.terminus.trantor2.service.dsl.properties.trigger.AgentSchedulerTrigger;
import io.terminus.trantor2.service.dsl.properties.trigger.AgentTrigger;
import io.terminus.trantor2.service.engine.impl.validation.CronExpressionValidator;
import io.terminus.trantor2.service.management.api.management.buildin.AgentBuiltinSkillToolHandler;
import io.terminus.trantor2.service.management.model.vo.AgentVO;
import io.terminus.trantor2.service.management.api.management.buildin.AgentBuiltinSkillToolHandler;
import io.terminus.trantor2.service.management.api.template.ServiceTemplateApi;
import io.terminus.trantor2.service.management.api.template.model.request.GenerateServiceByTemplateKeyRequest;
import io.terminus.trantor2.service.management.model.po.ServicePO;
import io.terminus.trantor2.service.management.model.request.CreateAgentFromModelRequest;
import io.terminus.trantor2.service.management.model.request.ServiceContextRequest;
import io.terminus.trantor2.service.management.model.request.ServiceGenerateRequest;
import io.terminus.trantor2.service.management.model.vo.ServiceContextVO;
import io.terminus.trantor2.service.management.model.vo.VariableVO;
import io.terminus.trantor2.service.management.parser.ServiceVariableParser;
import io.terminus.trantor2.service.management.service.ServiceMetaService;
import io.terminus.trantor2.service.management.service.ServiceSchedulerJobService;
import io.terminus.trantor2.service.management.template.TemplateEngine;
import io.terminus.trantor2.trigger.service.TriggerManageService;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * AgentManagementController
 *
 * <AUTHOR> Created on 2025/5/6 16:37
 */
@Tag(name = "Agent配置管理")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/trantor/agent/management")
public class AgentManagementController {

    private static final Map<String, String> TEMPLATE_MAP = new ConcurrentHashMap<>();

    private final ServiceMetaService serviceMetaService;
    private final ServiceSchedulerJobService serviceSchedulerJobService;
    private final ServiceVariableParser serviceVariableParser;
    private final ServiceTemplateApi serviceTemplateApi;
    private final TriggerManageService triggerManageService;
    private final List<AgentBuiltinSkillToolHandler> builtinSkillToolHandlers;

    @Operation(summary = "新增Agent")
    @PostMapping(value = "/create")
    public Response<Boolean> createAgent(@RequestBody AgentVO agentVO) {
        Optional<AIAgentMeta> aiAgentMetaOptional = serviceMetaService.findAgentByAgentKey(agentVO.getAgentKey(), TrantorContext.getTeamId());
        if (aiAgentMetaOptional.isPresent()) {
            throw new ServiceException(ErrorType.SERVICE_DEFINITION_KEY_DUPLICATED);
        }
        return saveAgent(agentVO);
    }

    @Operation(summary = "保存Agent")
    @PostMapping(value = "/save")
    public Response<Boolean> saveAgent(@RequestBody AgentVO agentVO) {
        if (agentVO.getAgentKey() == null) {
            agentVO.setAgentKey(agentVO.getAgent().getKey());
        }
        if (agentVO.getAgentName() == null) {
            agentVO.setAgentName(agentVO.getAgent().getName());
        }
        if (agentVO.getDescription() == null) {
            agentVO.setDescription(agentVO.getAgent().getDesc());
        }

        createAgentTrigger(agentVO.getAgent().getKey(), agentVO.getAgent().getProps().getTriggers());

        String dataStatisticServiceKey = agentVO.getAgentKey() + "_DATA_STATISTIC_SERVICE";
        if (CollectionUtils.isEmpty(agentVO.getAgent().getProps().getDataScopes())) {
            // 查看数据访问权限对应的服务是否存在，若存在删除此服务，并从 skillTools 中剔除此服务
            deleteDataStatisticService(dataStatisticServiceKey);
            removeDataStatisticServiceFromSkillTools(dataStatisticServiceKey, agentVO);
        } else {
            Optional<AIAgentMeta> aiAgentMetaOptional = serviceMetaService.findAgentByAgentKey(agentVO.getAgentKey(), TrantorContext.getTeamId());
            if (aiAgentMetaOptional.isPresent() && !isSameLlmModel(agentVO.getAgent(), aiAgentMetaOptional.get().getResourceProps().getAgent())) {
                // 若 agent 的 Llm 切换，需要删掉数据访问范围自动添加的工具，然后重新添加，保证自动添加的服务工具使用的 Llm 一致
                deleteDataStatisticService(dataStatisticServiceKey);
                removeDataStatisticServiceFromSkillTools(dataStatisticServiceKey, agentVO);
            }
            // 创建数据访问权限对应的服务并将该服务添加至 skillTools
            createAndEnableDataStatisticService(dataStatisticServiceKey, agentVO);
            // 追加新增的服务至 skillTools 中
            appendStatisticServiceToSkillToolsIfNotExist(dataStatisticServiceKey, agentVO);
        }

        handleBuiltinSkillTools(agentVO);
        serviceMetaService.saveAgent(agentVO);
        return Response.ok(Boolean.TRUE);
    }

    @SneakyThrows
    @Operation(summary = "根据模型创建Agent")
    @PostMapping(value = "/create-based-model")
    public Response<Boolean> createAgentFromModel(@RequestBody @Valid CreateAgentFromModelRequest request) {
        String template = TEMPLATE_MAP.computeIfAbsent("/agent/CREATE_AGENT_BASED_MODEL.json", (path) -> {
            try {
                Resource resource = new ClassPathResource(path);
                return IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        Map<String, Object> params = JsonUtil.toMap(request);
        params.put("module", KeyUtil.moduleKey(request.getAgentKey()));
        params.put("modelNames", request.getRelatedModels().stream().map(RelatedModel::getModelName).collect(Collectors.joining(",")));

        String agentDsl = TemplateEngine.apply(template, params);
        Agent agent = JsonUtil.fromJson(agentDsl, Agent.class);

        AgentVO agentVO = new AgentVO();
        agentVO.setAgentKey(request.getAgentKey());
        agentVO.setAgentName(request.getAgentName());
        agentVO.setDescription(request.getDescription());
        agentVO.setAccess(request.getAccess());
        agentVO.setParentKey(request.getParentKey());
        agentVO.setIsEnabled(request.getIsEnabled());
        agentVO.setAgent(agent);

        serviceMetaService.saveAgent(agentVO);
        return Response.ok(Boolean.TRUE);
    }

    private void createAgentTrigger(String agentKey, List<AgentTrigger> triggers) {
        // 检测是否配置了触发器, 如果配置了则对触发器进行实例化
        if (CollectionUtils.isNotEmpty(triggers)) {
            triggers.forEach(trigger -> {
                String agentTriggerKey = trigger.getKey();
                String agentTriggerName = trigger.getName();
                // 如果没有配置名称则动态生成一个
                if (StringUtils.isBlank(agentTriggerName)) {
                    agentTriggerName = String.format("%s_Trigger", trigger.getTool().getName());
                }
                if (trigger.getType().equals(AgentTriggerType.Scheduler)) {
                    String crontab = ((AgentSchedulerTrigger) trigger).getCrontab();
                    // 验证 crontab 表达式，确保周期时间不小于1分钟
                    CronExpressionValidator.validateCronExpression(crontab);

                    // 调度任务的params不能太大，字段长度要小于1024，该对象作为精简参数
                    AgentTrigger agentTrigger = new AgentTrigger();
                    agentTrigger.setKey(agentTriggerKey);
                    agentTrigger.setName(agentTriggerName);
                    agentTrigger.setConversationContent(trigger.getConversationContent());
                    agentTrigger.setNotificationConfig(trigger.getNotificationConfig());

                    SchedulerJob schedulerJob = new SchedulerJob();
                    schedulerJob.setJobKey(String.format("%s_%s_JOB", agentKey, agentTriggerKey));
                    schedulerJob.setJobName(agentTriggerName);
                    schedulerJob.setDesc(trigger.getDesc());
                    schedulerJob.setJobType(JobTypeEnum.Crontab.name());
                    schedulerJob.setExpression(crontab);
                    schedulerJob.setParams(JsonUtils.toJson(agentTrigger));
                    schedulerJob.setExecType("TAgent");
                    schedulerJob.setEnable(true);
                    boolean exists = serviceSchedulerJobService.existsSchedulerJob(agentKey, schedulerJob);
                    if (exists) {
                        serviceSchedulerJobService.asyncUpdateSchedulerJob(agentKey, schedulerJob, true);
                    } else {
                        serviceSchedulerJobService.asyncCreateSchedulerJob(agentKey, schedulerJob, true);
                    }
                } else if (trigger.getType().equals(AgentTriggerType.Event)) {
                    String eventKey = ((AgentEventTrigger) trigger).getEventKey();
                    String listenerName = String.format("监听器_%s", agentTriggerName);
                    triggerManageService.quickCreate(agentKey, agentTriggerKey, eventKey, listenerName);
                } else {
                    log.warn("当前触发器类型暂不支持");
                }
            });
        }
    }

    private boolean isSameLlmModel(Agent agent, Agent dbAgent) {
        return StringUtils.equals(agent.getProps().getModel().getModelPublisher(), dbAgent.getProps().getModel().getModelPublisher())
                && StringUtils.equals(agent.getProps().getModel().getName(), dbAgent.getProps().getModel().getName());
    }

    private void deleteDataStatisticService(String serviceKey) {
        Optional<ServicePO> targetService = serviceMetaService.findByServiceKey(serviceKey, TrantorContext.getTeamId());
        if (targetService.isPresent()) {
            serviceMetaService.delete(TrantorContext.getTeamId(), serviceKey);
        }
    }

    private void removeDataStatisticServiceFromSkillTools(String dataStatisticServiceKey, AgentVO agentVO) {
        List<SkillTool> skillTools = agentVO.getAgent().getProps().getSkillTools();
        if (CollectionUtils.isEmpty(skillTools)) {
            return;
        }

        // 删除数据统计服务工具
        skillTools.removeIf(tool -> tool.getKey().equals(dataStatisticServiceKey));
    }

    private void appendStatisticServiceToSkillToolsIfNotExist(String dataStatisticServiceKey, AgentVO agentVO) {
        if (CollectionUtils.isNotEmpty(agentVO.getAgent().getProps().getSkillTools())) {
            // 如果 agent 中已有数据统计服务工具则不再重复添加
            if (agentVO.getAgent().getProps().getSkillTools().stream().anyMatch(tool -> tool.getKey().equals(dataStatisticServiceKey))) {
                return;
            }
        }

        ServiceTool tool = new ServiceTool(dataStatisticServiceKey);
        tool.setName(agentVO.getAgentName() + "数据统计服务");

        List<Field> input = new ArrayList<>();
        Field userPromptField = new BaseField("userContent", "用户提示词", FieldType.Text);
        userPromptField.setRequired(true);
        input.add(userPromptField);
        Field agentKeyField = new BaseField("agentKey", "智能体 key", FieldType.Text);
        agentKeyField.setDefaultValue(agentVO.getAgentKey());
        agentKeyField.setRequired(true);
        input.add(agentKeyField);

        tool.setInput(input);
        tool.setFinalOutput(true);
        ServicePO serviceMeta = serviceMetaService.findByServiceKey(dataStatisticServiceKey, TrantorContext.getTeamId()).orElse(null);
        if (serviceMeta != null) {
            if (serviceMeta.getServiceDslJson() != null && StringUtils.isNotEmpty(serviceMeta.getServiceDslJson().getDesc())) {
                tool.setDesc(serviceMeta.getServiceDslJson().getDesc());
            } else if (serviceMeta.getServiceDoc() != null && StringUtils.isNotEmpty(serviceMeta.getServiceDoc().getDescription())) {
                tool.setDesc(serviceMeta.getServiceDoc().getDescription());
            } else {
                tool.setDesc("数据报表统计服务，负责根据用户输入及 agent 配置的持久模型范围进行数据统计并渲染成图表，常见的图表形式有：柱状图/折线图/饼状图等，用户常见的输入方式有：按 xx 统计/查看xx变化趋势/按xx 查看/环比/同比/汇总xx 等");
            }
        }
        agentVO.getAgent().getProps().getSkillTools().add(tool);
    }

    private void createAndEnableDataStatisticService(String serviceKey, AgentVO agentVO) {
        Optional<ServicePO> targetService = serviceMetaService.findByServiceKey(serviceKey, TrantorContext.getTeamId());
        if (targetService.isPresent()) {
            return;
        }

        ServiceGenerateRequest request = new ServiceGenerateRequest();
        request.setTeamId(TrantorContext.getTeamId());
        request.setServiceType(ServiceType.PROGRAMMABLE);
        request.setOpenTransaction(false);
        request.setTemplateKey("agent_data_statistic_service_template");
        request.setParentKey(agentVO.getParentKey());
        request.setServiceKey(serviceKey);
        request.setServiceName(agentVO.getAgentName() + "数据统计服务");

        GenerateServiceByTemplateKeyRequest generateRequest = getGenerateServiceByTemplateKeyRequest(request);
        generateRequest.setModelPublisher(agentVO.getAgent().getProps().getModel().getModelPublisher());
        generateRequest.setModelName(agentVO.getAgent().getProps().getModel().getName());
        serviceTemplateApi.generate(generateRequest);

        // 启用服务
        serviceMetaService.enable(TrantorContext.getTeamId(), serviceKey);
    }

    @NotNull
    private static GenerateServiceByTemplateKeyRequest getGenerateServiceByTemplateKeyRequest(ServiceGenerateRequest request) {
        GenerateServiceByTemplateKeyRequest generateRequest = new GenerateServiceByTemplateKeyRequest();
        generateRequest.setServiceKey(request.getServiceKey());
        generateRequest.setServiceName(request.getServiceName());
        generateRequest.setModelKey(request.getModelKey());
        generateRequest.setTemplateKey(request.getTemplateKey());
        generateRequest.setTeamId(request.getTeamId());
        generateRequest.setParentKey(request.getParentKey());
        generateRequest.setDescription(request.getDescription());
        generateRequest.setOpenTransaction(request.getOpenTransaction());
        generateRequest.setPermissionKey(request.getPermissionKey());
        return generateRequest;
    }

    @Operation(summary = "查询Agent变量上下文")
    @PostMapping(value = "/context")
    public Response<ServiceContextVO> context(@RequestBody ServiceContextRequest request) {
        Agent agent = JsonUtil.NON_INDENT_NON_EMPTY.fromJson(request.getServiceDsl(), Agent.class);
        if (agent == null) {
            throw new ServiceException(ErrorType.DSL_DEFINITION_INCORRECT);
        }

        List<VariableVO> variables = new ArrayList<>(2);

        // 智能体变量
        if (agent.getProps().getInput() != null) {
            variables.add(new VariableVO(VariableType.REQUEST.getKey(), "智能体入参", agent.getProps().getInput()));
        }

        // 系统变量
        variables.add(serviceVariableParser.buildSystemVariable());
        // 提示词变量
        VariableVO promptVariables = serviceVariableParser.buildPromptVariable();
        if (promptVariables != null) {
            variables.add(promptVariables);
        }

        ServiceContextVO contextVO = new ServiceContextVO();
        contextVO.setVariables(variables);

        return Response.ok(contextVO);
    }

    @Operation(summary = "查询Agent的开场白欢迎词变量上下文")
    @PostMapping(value = "/greetings/context")
    public Response<ServiceContextVO> greetingsContext(@RequestBody ServiceContextRequest request) {
        Agent agent = JsonUtil.NON_INDENT_NON_EMPTY.fromJson(request.getServiceDsl(), Agent.class);
        if (agent == null) {
            throw new ServiceException(ErrorType.DSL_DEFINITION_INCORRECT);
        }

        List<VariableVO> variables = new ArrayList<>(2);

        // 工具变量
        List<SkillTool> skillTools = agent.getProps().getGreetingsRelatedTools();
        if (CollectionUtils.isNotEmpty(skillTools)) {
            for (SkillTool skillTool : skillTools) {
                if (skillTool instanceof ServiceTool serviceTool) {
                    String tempVariableName = VariableType.NODE_OUTPUT.formatKey(skillTool.getKey());
                    variables.add(new VariableVO(tempVariableName, String.format("[%s]工具出参", skillTool.getName()),
                            serviceTool.getOutput()));
                } else if (skillTool instanceof HttpTool httpTool) {
                    String tempVariableName = VariableType.NODE_OUTPUT.formatKey(skillTool.getKey());
                    variables.add(new VariableVO(tempVariableName, String.format("[%s]工具出参", skillTool.getName()),
                            httpTool.getOutput()));
                }
            }
        }

        // 系统变量
        variables.add(serviceVariableParser.buildSystemVariable());

        // 提示词变量
        VariableVO promptVariables = serviceVariableParser.buildPromptVariable();
        if (promptVariables != null) {
            variables.add(promptVariables);
        }

        ServiceContextVO contextVO = new ServiceContextVO();
        contextVO.setVariables(variables);

        return Response.ok(contextVO);
    }

    private void handleBuiltinSkillTools(AgentVO agentVO) {
        if (CollectionUtils.isEmpty(builtinSkillToolHandlers)) {
            return;
        }

        AgentProperties props = agentVO.getAgent().getProps();
        synchronized (props) {
            List<SkillTool> skillTools = props.getSkillTools();
            if (skillTools == null) {
                skillTools = new ArrayList<>();
                props.setSkillTools(skillTools);
            }
            Map<String, SkillTool> toolMap = new LinkedHashMap<>();

            for (SkillTool tool : skillTools) {
                toolMap.put(tool.getKey(), tool);
            }

            for (AgentBuiltinSkillToolHandler handler : builtinSkillToolHandlers) {
                handler.handle(agentVO).ifPresent(newTool -> toolMap.put(newTool.getKey(), newTool));
            }

            skillTools.clear();
            skillTools.addAll(toolMap.values());
        }
    }
}
