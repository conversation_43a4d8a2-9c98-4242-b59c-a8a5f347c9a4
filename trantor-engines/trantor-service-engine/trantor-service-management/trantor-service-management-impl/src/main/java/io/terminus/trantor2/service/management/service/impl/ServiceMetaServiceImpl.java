package io.terminus.trantor2.service.management.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.doc.api.dto.PermissionDTO;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.permission.management.api.service.PermissionKeyInitializer;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.common.meta.AIAgentMeta;
import io.terminus.trantor2.service.common.meta.ServiceMeta;
import io.terminus.trantor2.service.common.meta.ServiceProps;
import io.terminus.trantor2.service.common.utils.BeanUtil;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemService;
import io.terminus.trantor2.service.engine.loader.SystemServiceLoader;
import io.terminus.trantor2.service.management.api.management.model.FindServiceByKeyRequest;
import io.terminus.trantor2.service.management.api.management.model.FindServiceByKeyResponse;
import io.terminus.trantor2.service.management.model.criteria.ServiceCriteria;
import io.terminus.trantor2.service.management.model.po.ServicePO;
import io.terminus.trantor2.service.management.model.po.ServicePO_;
import io.terminus.trantor2.service.management.model.vo.AgentVO;
import io.terminus.trantor2.service.management.model.vo.ServiceDoc;
import io.terminus.trantor2.service.management.repo.AIAgentRepo;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import io.terminus.trantor2.service.management.service.ServiceMetaService;
import io.terminus.trantor2.service.management.service.ServiceSchedulerJobService;
import io.terminus.trantor2.service.management.service.consts.ServiceMetaConst;
import io.terminus.trantor2.service.management.validator.ServiceMetaValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ServiceMetaServiceImpl implements ServiceMetaService {
    private final MetaQueryService queryService;
    private final ServiceRepo serviceRepo;
    private final AIAgentRepo aiAgentRepo;

    private final List<SystemServiceLoader> systemServiceLoaders;

    private final ServiceMetaValidator serviceMetaValidator;
    private final TeamService teamService;
    private final PermissionKeyInitializer permissionKeyInitializer;

    private final ServiceSchedulerJobService serviceSchedulerJobService;

    @Deprecated
    @Override
    public Optional<ServicePO> findById(Long id) {
        return queryService.findById(id).map(this::tree2service);
    }

    @Override
    public Optional<ServicePO> findByMd5Code(String serviceDslMd5, Long teamId) {
        Cond cond = Cond.and(
                serviceTypeCond(),
                Field.props(String.class, ServicePO_.SERVICE_DSL_MD5).equal(serviceDslMd5)
        );
        ServiceMeta node = serviceRepo.findOne(cond, ResourceContext.newResourceCtx(teamService.getTeamCode(teamId), getUserId(false))).orElse(null);
        if (node == null) {
            return Optional.empty();
        }
        String key = node.getKey();
        return findByServiceKey(key, teamId);
    }

    @Override
    public Optional<ServicePO> findByServiceKey(String serviceKey, Long teamId) {
        Optional<ServiceMeta> obj = serviceRepo.findOneByKey(serviceKey, ResourceContext.newResourceCtx(teamService.getTeamCode(teamId), null));
        return obj.map(this::convert2PO);
    }

    public Optional<AIAgentMeta> findAgentByAgentKey(String serviceKey, Long teamId) {
        return aiAgentRepo.findOneByKey(serviceKey, ResourceContext.newResourceCtx(teamService.getTeamCode(teamId), null));
    }

    @Override
    public Paging<ServicePO> paging(ServiceCriteria serviceCriteria) {
        List<Cond> conds = Lists.newArrayList();
        {
            conds.add(serviceTypeCond());
            if (StringUtils.isNotEmpty(serviceCriteria.getKeyword())) {
                conds.add(
                        Cond.or(
                                Field.key().like("%" + serviceCriteria.getKeyword() + "%"),
                                Field.name().like("%" + serviceCriteria.getKeyword() + "%")
                        )
                );
            }
            // 查询业务流名称
            if (StringUtils.isNotEmpty(serviceCriteria.getName())) {
                conds.add(Field.name().like("%" + serviceCriteria.getName() + "%"));
            }
            if (StringUtils.isNotEmpty(serviceCriteria.getKey())) {
                conds.add(Field.key().equal(serviceCriteria.getKey()));
            }
            if (StringUtils.isNotEmpty(serviceCriteria.getParentKey())) {
                conds.add(Field.parentKey().equal(serviceCriteria.getParentKey()));
            }
            if (CollectionUtils.isNotEmpty(serviceCriteria.getServiceTypes())) {
                conds.add(Field.props(String.class, ServicePO_.SERVICE_TYPE).in(serviceCriteria.getServiceTypes().stream().map(Enum::name).toArray(Object[]::new)));
            } else if (Objects.nonNull(serviceCriteria.getServiceType())) {
                List<String> types = new ArrayList<>(2);
                types.add(serviceCriteria.getServiceType().name());
                if (serviceCriteria.getServiceType() == ServiceType.PROGRAMMABLE) {
                    types.add(ServiceType.SYSTEM.name());
                }
                conds.add(Field.props(String.class, ServicePO_.SERVICE_TYPE).in(types.toArray(new Object[0])));
            }
            if (CollectionUtils.isNotEmpty(serviceCriteria.getModelKeyList())) {
                conds.add(Field.props(String.class, ServicePO_.MODEL_KEY).in(serviceCriteria.getModelKeyList()));
            }
            if (Objects.nonNull(serviceCriteria.getIsEnabled())) {
                conds.add(Field.props(Boolean.class, ServicePO_.IS_ENABLED).equal(serviceCriteria.getIsEnabled()));
            }
            // 查询创建时间区间
            if (Objects.nonNull(serviceCriteria.getStartTime())) {
                conds.add(Field.props(Date.class, ServicePO_.CREATED_AT).greaterThanOrEqual(serviceCriteria.getStartTime()));
            }
            if (Objects.nonNull(serviceCriteria.getEndTime())) {
                conds.add(Field.props(Date.class, ServicePO_.CREATED_AT).lessThanOrEqual(serviceCriteria.getEndTime()));
            }
            // 查询更新时间区间
            if (Objects.nonNull(serviceCriteria.getUpdateStartTime())) {
                conds.add(Field.props(Date.class, ServicePO_.UPDATED_AT).greaterThanOrEqual(serviceCriteria.getUpdateStartTime()));
            }
            if (Objects.nonNull(serviceCriteria.getUpdateEndTime())) {
                conds.add(Field.props(Date.class, ServicePO_.UPDATED_AT).lessThanOrEqual(serviceCriteria.getUpdateEndTime()));
            }
        }
        Cond cond = Cond.and(conds.toArray(new Cond[0]));
        int pageNumber = serviceCriteria.getPageNumber() == null ? 1 : serviceCriteria.getPageNumber();
        int pageSize = serviceCriteria.getPageSize() == null ? 20 : serviceCriteria.getPageSize();
        Paging<ServiceMeta> result = serviceRepo.findAll(cond, PageReq.of(pageNumber - 1, pageSize, Order.byModifiedAt().desc()), ResourceContext.newResourceCtx(teamService.getTeamCode(serviceCriteria.getTeamId()), getUserId(false)));
        List<ServicePO> data = new ArrayList<>();
        if (result.getData() != null) {
            result.getData().forEach(it -> {
                data.add(convert2PO(it));
            });
        }
        Paging<ServicePO> paging = new Paging<>();
        paging.setData(data);
        paging.setTotal(result.getTotal());
        return paging;
    }

    @Override
    public ServicePO create(ServicePO servicePO) {
        serviceMetaValidator.validate(servicePO);
        servicePO.setIsEnabled(false);
        servicePO.setIsDeleted(false);
        servicePO.setCreatedAt(new Date());
        servicePO.setCreatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(1L));
        servicePO.setUpdatedAt(new Date());
        servicePO.setUpdatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(1L));

        MetaEditAndQueryContext ctx = EditUtil.newCtx(servicePO.getTeamId(), getUserId(true));
        ServiceMeta meta = convert2Meta(servicePO);
        if (StringUtils.isBlank(servicePO.getParentKey())) {
            servicePO.setParentKey(getRootKey(ctx));
        }
        permissionKeyInitializer.initServicePermission(meta);    // 初始化服务权限项
        Long id = serviceRepo.create(meta, ResourceContext.newResourceCtx(teamService.getTeamCode(ctx.getTeamId()), ctx.getUserId()));
        servicePO.setId(id);
        return servicePO;
    }

    @Override
    public void saveAgent(AgentVO agentVO) {
        String teamCode = teamService.getTeamCode(TrantorContext.getTeamId());
        ResourceContext resourceContext = ResourceContext.newResourceCtx(teamCode, getUserId(true));

        Optional<AIAgentMeta> serviceOptional = aiAgentRepo.findOneByKey(agentVO.getAgentKey(), resourceContext);
        if (!serviceOptional.isPresent()) {
            if (StringUtils.isBlank(agentVO.getParentKey())) {
                agentVO.setParentKey(KeyUtil.newKeyUnderModule(KeyUtil.moduleKey(agentVO.getAgentKey()), KeyUtil.UNGROUP_FOLDER));
            }
            if (agentVO.getAccess() == null) {
                agentVO.setAccess(MetaNodeAccessLevel.Private);
            }
            AIAgentMeta createServiceMeta = convert2AgentMeta(agentVO);
            createServiceMeta.setCreatedAt(new Date());
            createServiceMeta.setCreatedBy(TrantorContext.getCurrentUserId());
            permissionKeyInitializer.initAIAgentPermission(createServiceMeta);  // 初始化智能体权限项
            aiAgentRepo.create(createServiceMeta, resourceContext);
        } else {
            if (StringUtils.isBlank(agentVO.getParentKey())) {
                agentVO.setParentKey(serviceOptional.get().getParentKey());
            }
            if (agentVO.getAccess() == null) {
                agentVO.setAccess(serviceOptional.get().getAccess());
            }
            AIAgentMeta updateServiceMeta = convert2AgentMeta(agentVO);
            updateServiceMeta.setUpdatedAt(new Date());
            updateServiceMeta.setUpdatedBy(TrantorContext.getCurrentUserId());
            aiAgentRepo.update(updateServiceMeta, resourceContext);
        }
    }

    /**
     * 同步 agent 指定的动态工具集范围至向量数据库
     * 有些场景用户无法在配置 agent 时静态指定好，需要运行时基于用户需求动态找到匹配的工具挂载至 agent，动态找匹配工具时需要给定范围，
     * 在给定的范围内查找。
     * @param agentVO
     */
    private void syncScopeToolsToVectorDB(AgentVO agentVO) {
        // 动态范围有三种类型: 1. 项目范围 2. 模块范围 3. 自定义范围

        // 1. 项目范围时，同步项目下的所有服务至向量数据库

        // 2. 模块范围时，同步模块下的所有服务至向量数据库

        // 3. 自定义范围时，同步自定义范围内的所有服务至向量数据库
    }

    private AIAgentMeta convert2AgentMeta(AgentVO agentVO) {
        AIAgentMeta node = new AIAgentMeta();
        {
            node.setKey(agentVO.getAgentKey());
            node.setName(agentVO.getAgentName());
            node.setParentKey(agentVO.getParentKey());
            node.setAccess(agentVO.getAccess());
            node.setDescription(agentVO.getDescription());
            AIAgentMeta.Props props = new AIAgentMeta.Props();
            props.setIsEnabled(agentVO.getIsEnabled());
            props.setAgent(agentVO.getAgent());
            node.setResourceProps(props);
        }
        return node;
    }

    @Override
    public ServicePO save(ServicePO servicePO) {
        serviceMetaValidator.validate(servicePO);
        ServiceDefinition originalDefinition = null;
        Optional<ServicePO> serviceOptional = findByServiceKey(servicePO.getServiceKey(), servicePO.getTeamId());

        if (serviceOptional.isPresent()) {
            ServicePO originService = serviceOptional.get();
            originalDefinition = originService.getServiceDslJson();
            BeanUtil.copyNullProperties(originService, servicePO);
            if (servicePO.getServiceDslJson() != null) {
                ServiceDefinition definition = fillDSL(servicePO);
                servicePO.setServiceDslJson(definition);
            }
        } else {
            ServiceDefinition definition = fillDSL(servicePO);
            servicePO.setServiceDslJson(definition);
            servicePO.setIsDeleted(false);
            servicePO.setCreatedAt(new Date());
            servicePO.setCreatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(1L));
            servicePO.setIsEnabled(Boolean.TRUE.equals(servicePO.getIsEnabled()));
        }

        servicePO.setUpdatedAt(new Date());
        servicePO.setUpdatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(1L));

        try {
            return save(servicePO, !serviceOptional.isPresent());
        } catch (Exception e) {
            log.error("save service error, cause:{}", Throwables.getStackTraceAsString(e));
            throw e;
        }
    }

    @Override
    public ServicePO disable(Long teamId, String serviceKey) {
        Optional<ServicePO> serviceOptional = findByServiceKey(serviceKey, teamId);
        if (serviceOptional.isPresent()) {
            ServicePO po = serviceOptional.get();
            po.setIsEnabled(false);
            po.setUpdatedAt(new Date());
            po.setUpdatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(1L));
            return save(po, false);
        } else {
            throw new ServiceException(ErrorType.SERVICE_DEFINITION_IS_NULL);
        }
    }

    @Override
    public ServicePO enable(Long teamId, String serviceKey) {
        Optional<ServicePO> serviceOptional = findByServiceKey(serviceKey, teamId);
        if (serviceOptional.isPresent()) {
            ServicePO po = serviceOptional.get();
            po.setIsEnabled(true);
            po.setUpdatedAt(new Date());
            po.setUpdatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(1L));
            return save(po, false);
        } else {
            throw new ServiceException(ErrorType.SERVICE_DEFINITION_IS_NULL);
        }
    }

    private ServiceDefinition fillDSL(ServicePO servicePO) {
        ServiceDefinition definition = servicePO.getServiceDslJson();
        definition.setKey(servicePO.getServiceKey());
        definition.setName(servicePO.getServiceName());
        return definition;
    }

    @Override
    public Boolean delete(Long teamId, String serviceKey) {
        serviceRepo.deleteByKey(serviceKey, ResourceContext.newResourceCtx(teamService.getTeamCode(teamId), getUserId(true)));
        return true;
    }

    @Override
    public void delete(String serviceKey) {
        Optional<ServicePO> opt = findByServiceKey(serviceKey, TrantorContext.getTeamId());
        if (opt.isPresent()) {
            delete(TrantorContext.getTeamId(), serviceKey);
            if (opt.get().getServiceDslJson().enableSchedulerTask()) {
                // @formatter:off
                serviceSchedulerJobService.asyncDeleteSchedulerJob(serviceKey,
                        opt.get().getServiceDslJson()
                                .getProps()
                                .getSchedulerJob()
                                .getJobKey());
                // @formatter:on
            }
        }
    }

    @Override
    public FindServiceByKeyResponse findByServiceKey(FindServiceByKeyRequest request) {
        FindServiceByKeyResponse response = new FindServiceByKeyResponse();
        ServicePO servicePO = findByServiceKey(request.getServiceKey(), request.getTeamId()).orElse(null);
        BeanUtil.copyNullProperties(servicePO, response);
        return response;
    }

    @Override
    public Boolean deleteByEventCode(String eventCode, Long teamId) {
        serviceRepo.deleteByKey(eventCode + ServiceMetaConst.SERVICE_SUFFIX, ResourceContext.newResourceCtx(teamService.getTeamCode(teamId), getUserId(true)));
        return true;
    }

    @Override
    public Boolean deleteByRuleKey(String ruleKey, Long teamId) {
        serviceRepo.deleteByKey(ruleKey + ServiceMetaConst.SERVICE_SUFFIX, ResourceContext.newResourceCtx(teamService.getTeamCode(teamId), getUserId(true)));
        return true;
    }

    @Override
    @Transactional
    public Map<String, String> initSysServices(String moduleKey, String parentKey, Long teamId, List<String> serviceKeys) {
        assert moduleKey != null;
        assert teamId != null;

        List<io.terminus.trantor2.service.engine.delegate.Service> systemServices = new ArrayList<>();
        systemServiceLoaders.forEach(loader -> systemServices.addAll(loader.findAll()));

        Map<String, String> errorMap = new HashMap<>();

        for (io.terminus.trantor2.service.engine.delegate.Service service : systemServices) {
            SystemService systemService = (SystemService) service;

            if (CollectionUtils.isNotEmpty(serviceKeys) && !serviceKeys.contains(systemService.getServiceKey())) {
                continue;
            }

            String fullKey = KeyUtil.newKeyUnderModule(moduleKey, systemService.getServiceKey());

            try {
                ServicePO servicePO = new ServicePO();
                servicePO.setServiceKey(fullKey);
                servicePO.setServiceType(ServiceType.SYSTEM);
                servicePO.setServiceName(systemService.getServiceName());
                servicePO.setServiceDslJson((ServiceDefinition) systemService.getMeta().getDefinition());
                servicePO.setIsEnabled(true);
                servicePO.setIsDeleted(false);
                servicePO.setCreatedAt(new Date());
                servicePO.setCreatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(1L));
                servicePO.setUpdatedAt(new Date());
                servicePO.setUpdatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(1L));
                servicePO.setTeamId(teamId);
                servicePO.setParentKey(parentKey);

                ServicePO serviceDB = findByServiceKey(fullKey, teamId).orElse(null);
                if (serviceDB != null) {
                    if (serviceDB.getParentKey() != null) {
                        servicePO.setParentKey(serviceDB.getParentKey());
                    }
                    // 把权限回填
                    servicePO.getServiceDslJson().getProps().setPermissionKey(serviceDB.getServiceDslJson().getProps().getPermissionKey());
                    servicePO.getServiceDslJson().getProps().setInputFieldRules(serviceDB.getServiceDslJson().getProps().getInputFieldRules());
                    servicePO.getServiceDslJson().getProps().setOutputFieldRules(serviceDB.getServiceDslJson().getProps().getOutputFieldRules());
                }

                save(servicePO, Objects.isNull(serviceDB));
            } catch (Exception e) {
                log.error("init system service error:{}", Throwables.getStackTraceAsString(e));
                errorMap.put(fullKey, e.getMessage());
            }
        }

        return errorMap;
    }

    /**
     * 获取全部编排服务元数据
     *
     * @param teamId    团队ID
     * @param moduleKey 模块Key
     * @return 该团队下全部的编排服务
     */
    @Override
    public List<ServicePO> findAllInModule(Long teamId, String moduleKey) {
        List<ServiceMeta> all = serviceRepo.findAll(Cond.and(
                        Field.key().like(moduleKey + "$%"),
                        Field.type().equal(MetaType.ServiceDefinition.name())),
                ResourceContext.newResourceCtx(teamService.getTeamCode(teamId), getUserId(false)));
        return all.stream()
                .map(this::convert2PO)
                .collect(Collectors.toList());
    }

    @Override
    public void savePermission(String serviceKey, Map<String, PermissionDTO> permissions) {
        ResourceContext ctx = ResourceContext.newResourceCtx(TrantorContext.getTeamCode(), TrantorContext.getCurrentUserId());
        ServiceMeta originNode = serviceRepo.findOneByKey(serviceKey, ctx)
                .orElseThrow(() -> new TrantorRuntimeException("service not found, key is " + serviceKey));
        originNode.getResourceProps().setPermissions(permissions);
        serviceRepo.update(originNode, ctx);
    }

    private ServicePO save(ServicePO servicePO, boolean create) {
        MetaEditAndQueryContext ctx = EditUtil.newCtx(servicePO.getTeamId(), getUserId(true));
        String teamCode = teamService.getTeamCode(ctx.getTeamId());
        if (create) {
            if (StringUtils.isBlank(servicePO.getParentKey())) {
                servicePO.setParentKey(KeyUtil.newKeyUnderModule(KeyUtil.moduleKey(servicePO.getServiceKey()), KeyUtil.UNGROUP_FOLDER));
            }
            if (servicePO.getAccess() == null) {
                servicePO.setAccess(MetaNodeAccessLevel.Private);
            }
            Long id = serviceRepo.create(convert2Meta(servicePO), ResourceContext.newResourceCtx(teamCode, ctx.getUserId()));
            servicePO.setId(id);
        } else {
            ServiceMeta originNode = serviceRepo.findOneByKey(servicePO.getServiceKey(), ResourceContext.newResourceCtx(teamCode, ctx.getUserId()))
                    .orElseThrow(() -> new TrantorRuntimeException("service not found, key is " + servicePO.getServiceKey()));
            if (StringUtils.isBlank(servicePO.getParentKey())) {
                servicePO.setParentKey(originNode.getParentKey());
            }
            if (servicePO.getAccess() == null) {
                servicePO.setAccess(originNode.getAccess());
            }
            ServiceMeta updateServiceMeta = convert2Meta(servicePO);
            updateServiceMeta.getResourceProps().setPermissions(originNode.getResourceProps().getPermissions());
            serviceRepo.update(updateServiceMeta, ResourceContext.newResourceCtx(teamCode, ctx.getUserId()));
        }
        return servicePO;
    }

    private String getRootKey(MetaEditAndQueryContext ctx) {
        return queryService.queryInApp(ctx).findFolderRootKey();
    }

    private ServicePO tree2service(MetaTreeNodeExt ent) {
        ServicePO result = new ServicePO();
        {
            result.setId(ent.getId());
            result.setServiceKey(ent.getKey());
            try {
                result.setServiceType(
                        ObjectJsonUtil.MAPPER.treeToValue(
                                ent.getProps().get(ServicePO_.SERVICE_TYPE),
                                ServiceType.class
                        )
                );
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            result.setServiceName(ent.getName());
            result.setParentKey(ent.getParentKey());
            result.setModelKey(ent.getProps().get(ServicePO_.MODEL_KEY).asText());
            result.setServiceDslMd5(ent.getProps().get(ServicePO_.SERVICE_DSL_MD5).asText());
            result.setServiceDslJson(JsonUtil.fromJson(getServiceDslJson(ent.getProps()), ServiceDefinition.class));
            result.setIsEnabled(ent.getProps().get(ServicePO_.IS_ENABLED).asBoolean());
            result.setIsDeleted(false); // omit isDeleted 'cause we do real delete in meta table
            result.setCreatedBy(ent.getCreatedBy());
            result.setCreatedAt(ent.getCreatedAt());
            result.setUpdatedBy(ent.getUpdatedBy());
            result.setUpdatedAt(ent.getUpdatedAt());
            result.setTeamId(ent.getTeamId());
            result.setAccess(ent.getAccess());
            result.setServiceDoc(convertDoc(ent.getDescription()));
        }
        return result;
    }

    private String getServiceDslJson(JsonNode props) {
        if (props == null) {
            return null;
        }
        JsonNode dsl = props.get(ServicePO_.SERVICE_DSL_JSON);
        if (dsl.isTextual()) {
            return dsl.asText();
        }
        try {
            return ObjectJsonUtil.MAPPER.writeValueAsString(dsl);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private Cond serviceTypeCond() {
        return Field.type().equal(MetaType.ServiceDefinition.name());
    }

    private ServiceMeta convert2Meta(ServicePO service) {
        ServiceDefinition definition = service.getServiceDslJson();

        ServiceMeta node = new ServiceMeta();
        {
            node.setKey(service.getServiceKey());
            node.setName(service.getServiceName());
            node.setParentKey(service.getParentKey());
            node.setAccess(service.getAccess());
            if (service.getServiceDoc() != null) {
                node.setDescription(JsonUtil.toJson(service.getServiceDoc()));
            }
            ServiceProps props = new ServiceProps();
            props.setServiceType(service.getServiceType());
            props.setModelKey(service.getModelKey());
            props.setServiceDslMd5(service.getServiceDslMd5());
            props.setServiceDslJson(definition);
            props.setIsEnabled(service.getIsEnabled());
            props.setEventProps(service.getEventProps());
            node.setResourceProps(props);
        }
        return node;
    }

    private ServicePO convert2PO(ServiceMeta source) {
        ServicePO target = new ServicePO();
        target.setId(source.getId());
        target.setServiceKey(source.getKey());
        target.setServiceType(source.getResourceProps().getServiceType());
        target.setServiceName(source.getName());
        target.setParentKey(source.getParentKey());
        target.setModelKey(source.getResourceProps().getModelKey());
        target.setServiceDslMd5(source.getResourceProps().getServiceDslMd5());
        target.setServiceDslJson(source.getResourceProps().getServiceDslJson());
        target.setIsEnabled(source.getResourceProps().getIsEnabled());
        target.setIsDeleted(false); // omit isDeleted 'cause we do real delete in meta table
        target.setServiceDoc(convertDoc(source.getDescription()));
        target.setCreatedBy(source.getCreatedBy());
        target.setCreatedAt(source.getCreatedAt());
        target.setUpdatedBy(source.getUpdatedBy());
        target.setUpdatedAt(source.getUpdatedAt());
        target.setTeamId(source.getTeamId());
        target.setAccess(source.getAccess());
        target.setExtended(source.getExtended());
        target.setExtensible(source.getExtensible());
        target.setCustomExt(source.getCustomExt());
        return target;
    }

    private ServiceDoc convertDoc(String description) {
        if (StringUtils.isNotBlank(description)) {
            try {
                return JsonUtil.NON_INDENT_NON_EMPTY.getObjectMapper().readValue(description, ServiceDoc.class);
            } catch (Exception ignored) {
                return new ServiceDoc(description);
            }
        }
        return new ServiceDoc();
    }

    private Long getUserId(boolean check) {
        return TrantorContext.safeGetCurrentUser().map(User::getId).orElseGet(() -> {
            if (check) {
                throw new ServiceException(ErrorType.SERVER_ERROR, "user not login");
            }
            return null;
        });
    }
}
