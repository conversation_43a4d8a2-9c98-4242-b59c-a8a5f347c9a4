package io.terminus.trantor2.service.management.service.impl.builtin;

import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.properties.BaseField;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.ai.ToolVisible;
import io.terminus.trantor2.service.management.api.management.buildin.AgentBuiltinSkillToolHandler;
import io.terminus.trantor2.service.management.model.vo.AgentVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class DeepResearchBuiltinSkillToolHandler implements AgentBuiltinSkillToolHandler {
    public static final String DEEP_RESEARCH_SKILL_TOOL_KEY = "sys_common$deep_research";

    @Override
    public Optional<SkillTool> handle(AgentVO agentVO) {
        if (!agentVO.getAgent().getProps().getDeepResearch().getEnabled()) {
            return Optional.empty();
        }

        ServiceTool tool = new ServiceTool(DEEP_RESEARCH_SKILL_TOOL_KEY);
        tool.setName("深入研究");
        tool.setDesc("深入研究服务");
        tool.setToolVisible(ToolVisible.allUnVisible());

        List<Field> input = new ArrayList<>();
        Field userPromptField = new BaseField("researchTopics", "研究主题", FieldType.Text);
        userPromptField.setRequired(true);
        input.add(userPromptField);

        Field agentKeyField = new BaseField("agentKey", "智能体 key", FieldType.Text);
        agentKeyField.setDefaultValue(agentVO.getAgentKey());
        agentKeyField.setRequired(true);
        input.add(agentKeyField);

        Field interruptFeedback = new BaseField("interruptFeedback", "中断反馈", FieldType.Text);
        interruptFeedback.setRequired(false);
        interruptFeedback.setDescription("可选参数，用于在研究过程中计划完成后，用户的中断反馈信息，用户若接受计划则传 accepted ，若需重新制定计划则传 edit_plan。");
        input.add(interruptFeedback);

        tool.setInput(input);
        tool.setFinalOutput(false);
        return Optional.empty();
    }
}
