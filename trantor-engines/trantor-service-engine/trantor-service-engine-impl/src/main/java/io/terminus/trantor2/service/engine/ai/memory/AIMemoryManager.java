package io.terminus.trantor2.service.engine.ai.memory;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.service.dsl.Agent;
import io.terminus.trantor2.service.dsl.properties.AgentLongTermMemoryConfig;
import io.terminus.trantor2.service.dsl.properties.AgentProperties;
import io.terminus.trantor2.service.engine.ai.agent.client.AgentClient;
import io.terminus.trantor2.service.engine.ai.agent.client.request.MemoryExtractAndUpdateRequest;
import io.terminus.trantor2.service.engine.ai.agent.client.response.MemoryExtractAndUpdateResponse;
import io.terminus.trantor2.service.engine.ai.web.request.AIGlobalMemoryRequest;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.loader.metadata.AgentMetadataQuery;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 *         <p>
 *         用于管理全局和 agent 长期记忆。
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AIMemoryManager {
    private final AIGlobalMemoryService globalMemoryService;
    private final AIAgentMemoryService agentMemoryService;
    private final AgentClient agentClient;
    private final AgentMetadataQuery agentMetadataQuery;
    /**
     *
     * 获取全局和 agent 长期记忆，并生成 prompt。
     *
     * @param agent agent
     * @return 生成的 prompt 字符串
     */
    public String generateMemoryPrompt(Agent agent) {
        if (agent == null || BooleanUtils.isNotTrue(agent.getProps().getLongTermMemoryConfig().getEnabled())) {
            return null;
        }
        Map<String, Object> globalMemory = globalMemoryService.getGlobalMemory();
        String globalMemoryPrompt = formatGlobalMemoryPrompt(globalMemory);

        List<AgentLongTermMemoryConfig.MemoryVariable> agentMemoryVariables = agentMemoryService.getMemoryVariables(agent);
        String agentVariablePrompt = agentMemoryVariables.stream()
                .filter(v -> StringUtils.isNotBlank(v.getValue()))
                .map(v -> v.getName() + "（" + v.getDescription() + "）" + ": " + v.getValue())
                .collect(Collectors.joining("\n"));

        List<Map<String, Object>> agentMemoryFragments = agentMemoryService.getMemoryFragments(agent.getKey());
        String agentFragmentsPrompt = agentMemoryFragments.stream()
                .map(f -> (String) f.get("content"))
                .collect(Collectors.joining("\n"));

        List<String> parts = new ArrayList<>();

        String prompt = "# Memory\n" +
                "当前 Agent 开启了长期记忆。\n";

        if (ObjectUtils.isNotEmpty(globalMemoryPrompt)) {
            parts.add("## 全局记忆\n" + globalMemoryPrompt);
        }
        if (ObjectUtils.isNotEmpty(agentVariablePrompt)) {
            parts.add("## 重点记忆\n" + agentVariablePrompt);
        }else {
            parts.add("## 重点记忆\n" + "无");
        }
        if (ObjectUtils.isNotEmpty(agentFragmentsPrompt)) {
            parts.add("## 辅助记忆\n" + agentFragmentsPrompt);
        }
        if (CollectionUtils.isEmpty(parts)) {
            return prompt;
        }
        String tips = ObjectUtils.isNotEmpty(agentVariablePrompt) ? "请优先参考【重点记忆】，" : "\n\n";
        return prompt + tips + "\n" +
                "## 记忆管理\n" +
                "你负责管理用户的长期记忆，仅记录稳定的且长期的个性化配置、习惯。\n" +
                "\n" +
                "**长期记忆判断标准：**\n" +
                "1. 与【重点记忆】强相关，且为长期偏好、习惯、配置。\n" +
                "2. 用户明确说“记住”“以后都按xxx”。\n" +
                "\n" +
                "**不要记忆以下情况：**\n" +
                "- 临时待办/提醒/计划\n" +
                "- 一次性业务请求或流程指令\n" +
                "\n" +
                "注意：长期记忆宁缺毋滥，不确定时请勿记录。\n" +
                "调用【更新长期记忆】工具只是流程顺带的部分，不是最终结果，回答应聚焦用户需求本身\n\n" +
                String.join("\n\n", parts);
    }

    public boolean updateMemory(String agentKey, String userContent) {
        if (agentKey == null) {
            return false;
        }
        Metadata agentMeta = agentMetadataQuery.findMeta(Key.of(TrantorContext.getTeamId(), agentKey));
        if (agentMeta == null || agentMeta.getDefinition() == null) {
            return false;
        }
        Agent agent = (Agent) agentMeta.getDefinition();

        AgentProperties props = agent.getProps();
        AgentLongTermMemoryConfig memoryConfig = props.getLongTermMemoryConfig();
        if (Boolean.TRUE.equals(memoryConfig.getEnabled()) && TrantorContext.getCurrentUserId() != null) {
            String desc = Stream.of(agent.getDesc(), agent.getName())
                    .filter(StringUtils::isNotBlank)
                    .findFirst()
                    .orElse(null);
            return updateMemory(props.getModel().getName(), userContent, agentKey, memoryConfig, desc);
        }
        return false;
    }

    public boolean updateMemory(String llm, String userContent, String agentKey,
                             AgentLongTermMemoryConfig longTermMemoryConfig, String agentDescription) {
        if (llm == null) {
            return false;
        }
        MemoryExtractAndUpdateRequest request = buildRequest(llm, agentKey, agentDescription, userContent);
        MemoryExtractAndUpdateResponse response = agentClient.extractAndUpdateMemory(request);
        if (response == null) {
            return false;
        }

        if (CollectionUtils.isEmpty(response.getUpdatedFragments())) {
            return false;
        }
        response.getUpdatedFragments().stream()
                .filter(it -> "delete".equals(it.getAction()))
                .forEach(it -> agentMemoryService.deleteMemoryFragment(agentKey, it.getId()));

        response.getUpdatedFragments().stream()
                .filter(it -> "update".equals(it.getAction()))
                .forEach(it -> agentMemoryService.updateMemoryFragment(agentKey, it.getId(), it.getText()));

        response.getUpdatedFragments().stream()
                .filter(it -> "add".equals(it.getAction()))
                .forEach(it -> {
                    try {
                        agentMemoryService.createMemoryFragment(agentKey, it.getText());
                    } catch (Exception e) {
                        log.warn("add memory fragment failed, agentKey={}, text={}, err={}", agentKey, it.getText(), e.getMessage());
                    }
                });

        response.getUpdatedVariables().stream().filter(it ->
                        longTermMemoryConfig.getMemoryVariables().stream().anyMatch(var ->
                                var.getName().equals(it.getName()) && var.getModelInferenceEnabled()))
                .forEach(it -> agentMemoryService.updateMemoryVariable(agentKey, it.getName(), it.getNewValue()));
        return true;
    }

    public void clearMemory(String agentKey) {
        agentMemoryService.deleteAgentAllMemoryByBatch(agentKey);
    }

    private MemoryExtractAndUpdateRequest buildRequest(String llm, String agentKey, String agentDescription, String userContent) {
        MemoryExtractAndUpdateRequest request = new MemoryExtractAndUpdateRequest();
        request.setModel(llm);
        request.setVariables(getAgentMemoryVariables(agentKey));
        request.setFragments(getAgentMemoryFragments(agentKey));
        request.setCurrentInput(userContent);
        request.setAgentDescription(agentDescription);
        return request;
    }

    private List<MemoryExtractAndUpdateRequest.MemoryVariable> getAgentMemoryVariables(@NonNull String agentKey) {
        return agentMemoryService.getMemoryVariables(agentKey).stream()
                .map(it -> MemoryExtractAndUpdateRequest.MemoryVariable.builder()
                        .name(it.getName())
                        .description(it.getDescription())
                        .oldValue(it.getValue())
                        .build())
                .collect(Collectors.toList());
    }

    private List<MemoryExtractAndUpdateRequest.MemoryFragment> getAgentMemoryFragments(@NonNull String agentKey) {
        return agentMemoryService.getMemoryFragments(agentKey).stream()
                .map(it -> MemoryExtractAndUpdateRequest.MemoryFragment.builder()
                        .id(it.get("id").toString())
                        .oldValue(it.get("content").toString())
                        .build())
                .collect(Collectors.toList());
    }

    private String formatGlobalMemoryPrompt(Map<String, Object> globalMemory) {
        StringBuilder sb = new StringBuilder();

        AIGlobalMemoryRequest.KEY_DESCRIPTION_MAP.forEach((key, desc) -> {
            Object value = globalMemory.get(key);
            if (ObjectUtils.isNotEmpty(value) && desc != null) {
                sb.append(String.format(desc, value)).append("\n");
            }
        });

        globalMemory.forEach((key, value) -> {
            if (!AIGlobalMemoryRequest.KEY_DESCRIPTION_MAP.containsKey(key) && ObjectUtils.isNotEmpty(value)) {
                sb.append(key).append("：").append(value).append("\n");
            }
        });

        return sb.toString().replaceAll("[\\n\\r]+$", "");
    }
}
