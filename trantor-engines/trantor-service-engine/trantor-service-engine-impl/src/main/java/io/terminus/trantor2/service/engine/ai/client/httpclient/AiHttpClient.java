package io.terminus.trantor2.service.engine.ai.client.httpclient;

import io.terminus.trantor2.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * AiHttpClient
 *
 * <AUTHOR> Created on 2025/3/18 23:57
 */
@Slf4j
public class AiHttpClient {

    private final OkHttpClient okHttpClient;
    private final boolean logStreamingResponses;
    private final List<AiHttpInterceptor> interceptors;

    public AiHttpClient(OkHttpClient okHttpClient, List<AiHttpInterceptor> interceptors) {
        this(okHttpClient, interceptors, true);
    }

    public AiHttpClient(OkHttpClient okHttpClient, List<AiHttpInterceptor> interceptors,
            boolean logStreamingResponses) {
        this.okHttpClient = okHttpClient;
        this.interceptors = interceptors;
        this.logStreamingResponses = logStreamingResponses;
    }

    public <R> String post(String url, R request) {
        return post(url, request, r -> r);
    }

    public <R, T> T post(String url, R request, Function<String, T> responseHandler) {
        String requestJson = JsonUtil.toJson(request);

        okhttp3.Request.Builder builder = new okhttp3.Request.Builder()
                .url(url)
                .post(RequestBody.create(requestJson, okhttp3.MediaType.get("application/json; charset=utf-8")));

        interceptors(builder);

        okhttp3.Request okHttpRequest = builder.build();

        try (okhttp3.Response response = okHttpClient.newCall(okHttpRequest).execute()) {
            if (response.body() == null) {
                throw new IOException("Response body is null");
            }

            String responseBody = response.body().string();

            if (response.isSuccessful()) {
                return responseHandler.apply(responseBody);
            } else {
                throw Util.toException(response);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public <R> StreamingFuture stream(String url, R request, StreamingHandler streamingHandler) {
        return stream(url, request, null, streamingHandler);
    }

    public <R> StreamingFuture stream(String url, R request, Map<String, String> header, StreamingHandler streamingHandler) {
        String requestJson = JsonUtil.toJson(request);

        if (logStreamingResponses) {
            log.debug("Sending request to {}, params:{}", url, requestJson);
        }

        okhttp3.Request.Builder builder = new okhttp3.Request.Builder()
                .url(url)
                .post(RequestBody.create(requestJson, okhttp3.MediaType.get("application/json; charset=utf-8")));

        interceptors(builder);

        if (header != null) {
            header.forEach(builder::header);
        }

        okhttp3.Request okHttpRequest = builder.build();

        StreamingFuture streamingFuture = new StreamingFuture();

        EventSourceListener eventSourceListener = new EventSourceListener() {

            @Override
            public void onOpen(@NonNull EventSource eventSource, @NonNull okhttp3.Response response) {
                if (streamingFuture.isCancelled()) {
                    eventSource.cancel();
                    return;
                }

                if (logStreamingResponses) {
                    Util.logResponse(response, log);
                }

                if (!response.isSuccessful()) {
                    try {
                        streamingHandler.onError(Util.toException(response));
                    } catch (IOException e) {
                        streamingHandler.onError(e);
                    }
                }

                streamingHandler.onStart();
            }

            @Override
            public void onEvent(@NonNull EventSource eventSource, String id, String type, @NonNull String data) {
                if (streamingFuture.isCancelled()) {
                    eventSource.cancel();
                    return;
                }

                if (logStreamingResponses) {
                    log.debug("onEvent() [type:{}, data: {}]", type, data);
                }

                if ("[DONE]".equals(data)) {
                    streamingHandler.onComplete();
                    return;
                }

                try {
                    streamingHandler.onMessage(StringUtils.defaultString(type), data);
                } catch (Exception e) {
                    streamingHandler.onError(e);
                }
            }

            @Override
            public void onClosed(@NonNull EventSource eventSource) {
                if (streamingFuture.isCancelled()) {
                    eventSource.cancel();
                    return;
                }

                if (logStreamingResponses) {
                    log.debug("onClosed()");
                }
            }

            @Override
            public void onFailure(@NonNull EventSource eventSource, Throwable t, okhttp3.Response response) {
                if (streamingFuture.isCancelled()) {
                    return;
                }

                if (logStreamingResponses) {
                    log.debug("onFailure()", t);
                    Util.logResponse(response, log);
                }

                if (t != null) {
                    streamingHandler.onError(t);
                } else {
                    try {
                        streamingHandler.onError(Util.toException(response));
                    } catch (IOException e) {
                        streamingHandler.onError(e);
                    }
                }
            }
        };

        EventSources.createFactory(okHttpClient).newEventSource(okHttpRequest, eventSourceListener);

        return streamingFuture;
    }

    private void interceptors(okhttp3.Request.Builder builder) {
        if (interceptors != null) {
            for (AiHttpInterceptor interceptor : interceptors) {
                interceptor.apply(builder);
            }
        }
    }
}
