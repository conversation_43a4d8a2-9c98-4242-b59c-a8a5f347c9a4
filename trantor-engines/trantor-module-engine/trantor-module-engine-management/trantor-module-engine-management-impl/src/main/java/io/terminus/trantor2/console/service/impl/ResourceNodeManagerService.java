package io.terminus.trantor2.console.service.impl;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.console.api.request.PagingQueryRequest;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.repository.ResourceRepository;
import io.terminus.trantor2.meta.api.service.ExtMetaService;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.editor.component.ResourceRegistry;
import io.terminus.trantor2.meta.exception.MetaException;
import io.terminus.trantor2.meta.exception.MetaNotFoundException;
import io.terminus.trantor2.meta.index.FlatTree;
import io.terminus.trantor2.meta.index.MetaIndexAsset;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.index.MetaIndexRefRepo;
import io.terminus.trantor2.meta.index.TempTree;
import io.terminus.trantor2.meta.management.service.MetaUsageResolver;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import io.terminus.trantor2.module.model.query.ModuleRelationQueryMode;
import io.terminus.trantor2.module.model.query.QueryAccessLevel;
import io.terminus.trantor2.module.model.query.QueryTemplate;
import io.terminus.trantor2.module.service.ModuleManageService;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.process.meta.ProcessMeta;
import io.terminus.trantor2.properties.TriggerMQProperties;
import io.terminus.trantor2.scene.management.service.SceneManagerQueryService;
import io.terminus.trantor2.scene.management.service.SceneManagerService;
import io.terminus.trantor2.service.management.service.ServiceMetaService;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@SuppressWarnings({"rawtypes", "unchecked"})
@RequiredArgsConstructor
public class ResourceNodeManagerService {
    private final TriggerMQProperties triggerMQProperties;
    private final ExtMetaService extMetaService;
    private final DataStructNodeService dataStructNodeService;
    private final ServiceMetaService serviceMetaService;
    private final SceneManagerService sceneManagerService;
    private final SceneManagerQueryService sceneManagerQueryService;
    private final MetaUsageResolver metaUsageResolver;
    private final ModuleManageService moduleManageService;
    private final TeamService teamService;
    private final MetaIndexRefRepo metaIndexRefRepo;
    private final MetaIndexAssetRepo metaIndexAssetRepo;

    public void save(@Nonnull MetaTreeNodeExt node, boolean isCreate) {
        validateNode(node);
        MetaType metaType = node.getMetaType();
        ResourceRepository repo = ResourceRegistry.getRepository(metaType)
                .orElseThrow(() -> new MetaException("unknown resource type: " + metaType));
        Class<? extends ResourceBaseMeta<?>> metaClass = ResourceRegistry.getMetaClass(metaType);
        ResourceBaseMeta<?> meta = BaseMeta.newInstanceFrom(node, metaClass);

        if (isCreate) {
            postProcessBeforeInitialization(node);
            repo.create(meta, ResourceContext.ctxFromThreadLocal());
        } else {
            repo.update(meta, ResourceContext.ctxFromThreadLocal());
        }
        syncProcess(node);
    }

    public void changeAccessLevel(@Nonnull MetaType metaType, @Nonnull String key, @Nonnull MetaNodeAccessLevel accessLevel) {
        ResourceBaseMeta resource = Optional.ofNullable(findOneByKey(metaType, key)).orElseThrow(() -> new MetaNotFoundException(metaType.name(), key));
        if (accessLevel.equals(resource.getAccess())) {
            return;
        }
        resource.setAccess(accessLevel);
        save(resource.convert(), false);
    }

    public ResourceBaseMeta findOneByKey(@Nonnull MetaType metaType, @Nonnull String key) {
        return findOneOptionalByKey(key, metaType).orElseThrow(() -> new MetaNotFoundException(metaType.name(), key));
    }

    public <R extends ResourceBaseMeta> Paging<R> paging(PageReq pageReq, MetaType type, PagingQueryRequest request) {
        ResourceRepository repo = ResourceRegistry.getRepository(type)
                .orElseThrow(() -> new MetaException("unknown resource type: " + type));
        Cond cond = Cond.all();
        if (request != null) {
            cond = request.buildCond(cond, type);
        }
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        if (MetaType.Scene.equals(type) && MetaContext.isEnhanced(ModuleInfo.of(ctx.getTeamCode(), TrantorContext.getModuleKey()))) {
            PagingQueryRequest.DefaultPagingQueryRequest req = (PagingQueryRequest.DefaultPagingQueryRequest) request;
            return (Paging<R>) sceneManagerQueryService.pagingScenes(req.getParentKey(), req.getFuzzyValue(), pageReq);
        } else {
            return repo.findAll(cond, pageReq, ctx);
        }
    }

    public void reset(@Nonnull String key) {
        extMetaService.delete(key, ResourceContext.ctxFromThreadLocal());
    }

    public void delete(@Nonnull String key, @Nonnull MetaType metaType,
                       Boolean recursive, Boolean verbose, Boolean force) {
        Long teamId = TrantorContext.getTeamId();
        switch (metaType) {
            case Model:
                dataStructNodeService.delete(teamId, Collections.singleton(key), force);
                return;
            case Scene:
                sceneManagerService.delete(key);
                return;
            case ServiceDefinition:
                serviceMetaService.delete(key);
                return;
        }

        if (notSupportCUDTypes().contains(metaType)) {
            throw new ValidationException("unsupported meta type: " + metaType);
        }
        ResourceRepository repo = ResourceRegistry.getRepository(metaType)
                .orElseThrow(() -> new MetaException("unknown resource type: " + metaType));
        repo.deleteByKey(key, recursive, verbose, force, ResourceContext.ctxFromThreadLocal());
    }

    private void validateNode(@NotNull MetaTreeNodeExt node) {
        if (StringUtils.isBlank(node.getKey())) {
            throw new ValidationException("key can not be null or empty");
        }
        if (StringUtils.isBlank(node.getName())) {
            throw new ValidationException("name can not be null or empty");
        }
        if (StringUtils.isBlank(node.getParentKey())) {
            throw new ValidationException("parent key can not be null or empty");
        }
        if (node.getMetaType() == null) {
            throw new ValidationException("meta type can not be null or empty");
        } else if (notSupportCUDTypes().contains(node.getMetaType())) {
            throw new ValidationException("unsupported meta type: " + node.getMetaType());
        }
    }

    private Optional<ResourceBaseMeta> findOneOptionalByKey(@Nonnull String key, @Nonnull MetaType metaType) {
        ResourceRepository repo = ResourceRegistry.getRepository(metaType)
                .orElseThrow(() -> new MetaException("unknown resource type: " + metaType));
        return repo.findOneByKey(key, ResourceContext.ctxFromThreadLocal());
    }

    private void syncProcess(MetaTreeNodeExt node) {
        // TODO: sync process should be a dedicated feature, not imply in this controller
        if (node == null) {
            return;
        }
        if (!Objects.equals(node.getType(), MetaType.Process.name())) {
            return;
        }
        ProcessMeta processMeta = BaseMeta.newInstanceFrom(node, ProcessMeta.class);
        //deploymentService.syncProcess(processMeta);
    }

    private void postProcessBeforeInitialization(MetaTreeNode node) {
        switch (node.getType()) {
            case "EventDefinition":
                // 事件的topic默认取环境变量中的消息topic
                if (node.getProps() != null) node.getProps().put("topic", triggerMQProperties.getTopic());
                break;
        }
    }

    /**
     * 资源选择树接口
     * 用于编辑文件时选择其他资源，整合 relation 接口和 paging 接口的功能
     */
    public TempTree findSelectTree(ModuleRelationQueryMode mode,
                                   QueryAccessLevel queryAccessLevel,
                                   QueryTemplate queryTemplate,
                                   Map<String, String> queryParams) {
        Long teamId = getTeamId();
        String currentModuleKey = TrantorContext.getModuleKey();
        if (currentModuleKey == null) {
            throw new TrantorRuntimeException("moduleKey is null");
        }

        // 根据 mode 获取相关模块
        List<String> moduleKeys = getModuleKeysByMode(currentModuleKey, mode);
        List<String> relatedModuleKeys = moduleKeys.stream()
                .filter(key -> !key.equals(currentModuleKey))
                .collect(java.util.stream.Collectors.toList());

        // 使用QueryTemplate#buildCond构建查询条件
        Cond queryCondition = null;
        if (queryTemplate != null) {
            // 传入 true 表示需要包含树结构（Module、FolderRoot、Folder）
            queryCondition = queryTemplate.buildCond(currentModuleKey, relatedModuleKeys, queryAccessLevel, queryParams, true);
        }

        return metaUsageResolver.findTree(teamId, moduleKeys, false, queryCondition);
    }

    /**
     * 基于时间过滤的资源树接口
     * 根据开始时间和结束时间过滤元数据，整体逻辑和 /tree 类似
     * 返回扁平化的树结构：modules -> paths -> leaf resource nodes
     */
    public FlatTree findTimeFilteredTree(Date startTime, Date endTime) {
        Long teamId = getTeamId();

        // 构建时间过滤条件
        Cond timeCond = null;
        if (startTime != null && endTime != null) {
            timeCond = Field.ext().modifiedAt().between(startTime, endTime);
        } else if (startTime != null) {
            timeCond = Field.ext().modifiedAt().greaterThanOrEqual(startTime);
        } else if (endTime != null) {
            timeCond = Field.ext().modifiedAt().lessThanOrEqual(endTime);
        }

        // 组合基础条件和时间条件（不再限制 moduleKey，项目级别）
        // 注意：需要包含 View 类型，否则会遗漏页面元数据
        Cond baseCond = Cond.or(
                Field.type().equal(MetaType.Module.name()),
                Field.type().equal(MetaType.FolderRoot.name()),
                Field.type().equal(MetaType.Folder.name()),
                Field.type().in(MetaType.getSearchableTypes()),
                Field.type().equal(MetaType.View.name())
        );

        // 当存在时间条件时，仅对可搜索类型应用时间过滤；模块与文件夹不受时间限制
        Cond finalCond = timeCond != null
                ? Cond.or(
                        Field.type().equal(MetaType.Module.name()),
                        Field.type().equal(MetaType.FolderRoot.name()),
                        Field.type().equal(MetaType.Folder.name()),
                        // 对可检索类型和 View 应用时间过滤
                        Cond.and(
                                Cond.or(
                                        Field.type().in(MetaType.getSearchableTypes()),
                                        Field.type().equal(MetaType.View.name())
                                ),
                                timeCond
                        )
                )
                : baseCond;

        // 查询所有模块key，项目级别
        List<String> allModuleKeys = metaIndexAssetRepo.find(teamId, MetaIndexAsset.Base.class, Field.type().equal(MetaType.Module.name()))
                .stream().map(MetaIndexAsset.Base::getKey).collect(Collectors.toList());

        // 获取原始的TempTree（包含所有模块）
        TempTree tempTree = metaUsageResolver.findTree(teamId, allModuleKeys, false, finalCond);

        // 转换为FlatTree
        return convertToFlatTree(tempTree);
    }

    /**
     * 获取时间过滤范围内的元数据关系映射
     * 返回 Map<String, List<String>>，其中 key 为元数据key，value 为与此元数据相关的元数据key列表
     * "相关"指的是 key 依赖的元数据，以及依赖 key 的元数据
     */
    public Map<String, List<String>> findTimeFilteredMetaRelations(Date startTime, Date endTime) {
        Long teamId = getTeamId();

        // 构建时间过滤条件，获取时间范围内的所有元数据
        Cond timeCond = null;
        if (startTime != null && endTime != null) {
            timeCond = Field.ext().modifiedAt().between(startTime, endTime);
        } else if (startTime != null) {
            timeCond = Field.ext().modifiedAt().greaterThanOrEqual(startTime);
        } else if (endTime != null) {
            timeCond = Field.ext().modifiedAt().lessThanOrEqual(endTime);
        }

        // 包含 View 类型，确保页面元数据参与关系计算
        Cond baseCond = Cond.or(
                Field.type().in(MetaType.getSearchableTypes()),
                Field.type().equal(MetaType.View.name())
        );

        Cond finalCond = timeCond != null ? Cond.and(baseCond, timeCond) : baseCond;

        // 使用 Lite 查询（Structure 包含引用关系）
        List<MetaIndexAsset.Lite> timeFilteredAssets = metaIndexAssetRepo.find(teamId, MetaIndexAsset.Lite.class, finalCond);
        Set<String> timeFilteredKeys = timeFilteredAssets.stream()
                .map(MetaIndexAsset.Lite::getKey)
                .collect(Collectors.toSet());

        // 构建“被依赖关系”（反向引用），只在已时间过滤的 metas 范围内考虑相互关系
        // 不再进行第二次全量查询（避免 metaIndexAssetRepo.find 再查一次）
        Map<String, Set<String>> reverseRefIndex = new HashMap<>();
        for (MetaIndexAsset.Lite asset : timeFilteredAssets) {
            if (asset == null || asset.getStructure() == null) {
                continue;
            }
            String sourceKey = asset.getKey();
            // refs
            if (asset.getStructure().getRefs() != null) {
                for (io.terminus.trantor2.lang.meta.Structure.Ref ref : asset.getStructure().getRefs()) {
                    String targetKey = ref.getTargetKey();
                    // 仅保留 target 也在时间过滤集合内的关系
                    if (StringUtils.isNotBlank(targetKey) && timeFilteredKeys.contains(targetKey)) {
                        reverseRefIndex.computeIfAbsent(targetKey, k -> new HashSet<>()).add(sourceKey);
                    }
                }
            }
            // links
            if (asset.getStructure().getLinks() != null) {
                for (io.terminus.trantor2.meta.api.dto.MetaLink link : asset.getStructure().getLinks()) {
                    String targetKey = link.getTargetKey();
                    if (StringUtils.isNotBlank(targetKey) && timeFilteredKeys.contains(targetKey)) {
                        reverseRefIndex.computeIfAbsent(targetKey, k -> new HashSet<>()).add(sourceKey);
                    }
                }
            }
        }

        // 预构建：sourceKey（在时间范围内） -> Set<targetKey>（target 也必须在时间过滤集合内）
        Map<String, Set<String>> outRefIndex = new HashMap<>();
        for (MetaIndexAsset.Lite asset : timeFilteredAssets) {
            if (asset == null || asset.getStructure() == null) {
                continue;
            }
            String sourceKey = asset.getKey();
            Set<String> targets = outRefIndex.computeIfAbsent(sourceKey, k -> new HashSet<>());
            if (asset.getStructure().getRefs() != null) {
                for (io.terminus.trantor2.lang.meta.Structure.Ref ref : asset.getStructure().getRefs()) {
                    String targetKey = ref.getTargetKey();
                    if (StringUtils.isNotBlank(targetKey) && timeFilteredKeys.contains(targetKey)) {
                        targets.add(targetKey);
                    }
                }
            }
            if (asset.getStructure().getLinks() != null) {
                for (io.terminus.trantor2.meta.api.dto.MetaLink link : asset.getStructure().getLinks()) {
                    String targetKey = link.getTargetKey();
                    if (StringUtils.isNotBlank(targetKey) && timeFilteredKeys.contains(targetKey)) {
                        targets.add(targetKey);
                    }
                }
            }
        }

        // 构建关系映射：key -> 相关的 key（包含依赖与被依赖）
        Map<String, List<String>> relationMap = new HashMap<>();
        for (String key : timeFilteredKeys) {
            Set<String> related = new HashSet<>();
            // 此 key 依赖的元数据
            related.addAll(outRefIndex.getOrDefault(key, Collections.emptySet()));
            // 依赖此 key 的元数据
            related.addAll(reverseRefIndex.getOrDefault(key, Collections.emptySet()));
            // 如果没有相关元数据，跳过（不放入空列表）
            if (!related.isEmpty()) {
                relationMap.put(key, new ArrayList<>(related));
            }
        }

        return relationMap;
    }

    /**
     * 将TempTree转换为FlatTree
     * 扁平化层级结构：modules -> paths -> leaf resource nodes
     */
    private FlatTree convertToFlatTree(TempTree tempTree) {
        FlatTree flatTree = new FlatTree();

        if (tempTree.getModules() == null) {
            return flatTree;
        }

        for (TempTree.Module tempModule : tempTree.getModules()) {
            FlatTree.Module flatModule = new FlatTree.Module();
            flatModule.setKey(tempModule.getKey());
            flatModule.setName(tempModule.getName());

            // 收集所有路径和对应的资源节点
            Map<String, List<FlatTree.ResourceNode>> pathToResources = new HashMap<>();

            // 遍历模块下的所有子节点，收集叶子资源节点
            if (tempModule.getChildren() != null) {
                for (TempTree.TreeNode child : tempModule.getChildren()) {
                    collectLeafResources(child, "", pathToResources);
                }
            }

            // 批量填充修改人和修改时间，避免每个节点单独查询
            try {
                // 收集所有资源key
                Set<String> keys = pathToResources.values().stream()
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .map(FlatTree.ResourceNode::getKey)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                if (!keys.isEmpty()) {
                    List<MetaIndexAsset.Lite> assets = metaIndexAssetRepo.find(
                            getTeamId(),
                            MetaIndexAsset.Lite.class,
                            Field.key().in(new ArrayList<>(keys))
                    );
                    Map<String, MetaIndexAsset.Lite> liteMap = assets == null ? Collections.emptyMap() :
                            assets.stream().filter(Objects::nonNull)
                                    .collect(Collectors.toMap(MetaIndexAsset.Lite::getKey, a -> a, (a, b) -> a));
                    for (List<FlatTree.ResourceNode> nodes : pathToResources.values()) {
                        if (nodes == null) continue;
                        for (FlatTree.ResourceNode rn : nodes) {
                            MetaIndexAsset.Lite lite = liteMap.get(rn.getKey());
                            if (lite != null) {
                                if (lite.getModifiedBy() != null) {
                                    rn.setModifiedBy(lite.getModifiedBy());
                                }
                                if (lite.getModifiedAt() != null) {
                                    rn.setModifiedAt(lite.getModifiedAt().getTime());
                                }
                            }
                        }
                    }
                }
            } catch (Exception ignored) {
                // 忽略批量填充失败，保证主流程可用
            }

            // 将路径和资源转换为PathNode（resources为空则不生成PathNode）
            for (Map.Entry<String, List<FlatTree.ResourceNode>> entry : pathToResources.entrySet()) {
                List<FlatTree.ResourceNode> resources = entry.getValue();
                if (resources == null || resources.isEmpty()) {
                    continue; // 如果 resources 是 empty，PathNode 就没必要存在了
                }
                // 按修改时间倒序排序，空值放在最后
                resources.sort(Comparator.comparing(
                        FlatTree.ResourceNode::getModifiedAt,
                        Comparator.nullsLast(Comparator.reverseOrder())
                ));
                FlatTree.PathNode pathNode = new FlatTree.PathNode();
                pathNode.setPath(entry.getKey());
                pathNode.setResources(resources);
                flatModule.getPaths().add(pathNode);
            }

            if (flatModule.getPaths() != null && !flatModule.getPaths().isEmpty()) {
                flatTree.getModules().add(flatModule);
            }
        }

        return flatTree;
    }

    /**
     * 递归收集叶子资源节点
     */
    private void collectLeafResources(TempTree.TreeNode node, String currentPath, Map<String, List<FlatTree.ResourceNode>> pathToResources) {
        if (node == null) {
            return;
        }

        // 构建当前节点的路径
        String nodePath = currentPath.isEmpty() ? node.getName() : currentPath + "/" + node.getName();

        // 如果是叶子节点（没有子节点或子节点为空），则作为资源节点
        if (node.getChildren() == null || node.getChildren().isEmpty()) {
            // 这是一个叶子资源节点
            FlatTree.ResourceNode resourceNode = new FlatTree.ResourceNode();
            resourceNode.setKey(node.getKey());
            resourceNode.setType(node.getType());
            resourceNode.setSubType(node.getSubType());
            resourceNode.setName(node.getName());
            resourceNode.setPath(nodePath);
            resourceNode.setInnerKey(node.getInnerKey());
            resourceNode.setInnerType(node.getInnerType());
            resourceNode.setInnerName(node.getInnerName());
            resourceNode.setIsInner(node.getIsInner());
            resourceNode.setExtended(node.getExtended());

            // 修改人和修改时间改为批量填充，避免每个节点单独查询导致的性能问题（在 convertToFlatTree 中统一处理）

            // 使用父路径作为分组路径
            String groupPath = currentPath.isEmpty() ? "/" : currentPath;
            pathToResources.computeIfAbsent(groupPath, k -> new ArrayList<>()).add(resourceNode);
        } else {
            // 如果有子节点，继续递归
            for (TempTree.TreeNode child : node.getChildren()) {
                collectLeafResources(child, nodePath, pathToResources);
            }
        }
    }

    /**
     * 根据查询模式获取模块键列表
     */
    private List<String> getModuleKeysByMode(String currentModuleKey, ModuleRelationQueryMode mode) {
        switch (mode) {
            case CURRENT:
                return Collections.singletonList(currentModuleKey);
            case RELATION:
            case ALL:
                // 使用现有的模块关系查询逻辑
                var moduleDTO = moduleManageService.queryModuleRelation(currentModuleKey, mode);
                List<String> moduleKeys = new java.util.ArrayList<>();
                moduleKeys.add(currentModuleKey); // 包含当前模块
                if (moduleDTO.getRelatedModules() != null) {
                    moduleKeys.addAll(moduleDTO.getRelatedModules()
                            .stream()
                            .map(dto -> dto.getKey())
                            .collect(java.util.stream.Collectors.toList()));
                }
                return moduleKeys;
            default:
                return Collections.singletonList(currentModuleKey);
        }
    }

    private Long getTeamId() {
        Long teamId = TrantorContext.getTeamId();
        if (teamId != null) {
            return teamId;
        }
        String teamCode = TrantorContext.getTeamCode();
        if (teamCode != null) {
            teamId = teamService.getTeamIdByCode(teamCode);
        }
        if (teamId == null) {
            throw new TrantorRuntimeException("teamId or teamCode is null");
        }
        return teamId;
    }

    /**
     * 增删改限制一些类型，避免接口越权、逻辑丢失等问题
     */
    private List<MetaType> notSupportCUDTypes() {
        return Arrays.asList(
                MetaType.Module,
                MetaType.Model,
                MetaType.Scene,
                MetaType.View,
                MetaType.MenuTree,
                MetaType.MenuIcon,
                MetaType.Root,
                MetaType.Folder,
                MetaType.PortalIcon,
                MetaType.ServiceDefinition,
                MetaType.ExtModule,
                MetaType.Ext,
                MetaType.ConnectorInst,
                MetaType.ErrorCodeRoot,
                MetaType.FolderRoot,
                MetaType.TakeCodeRoot
        );
    }
}
