package io.terminus.trantor2.module.mcp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.module.mcp.dto.BuiltInMcpServersResponse;
import io.terminus.trantor2.module.mcp.dto.McpServerDto;
import io.terminus.trantor2.module.mcp.dto.McpToolDto;
import io.terminus.trantor2.module.mcp.service.McpToolDiscoveryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 内置MCP服务器列表控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/trantor/mcp/built-in")
@Tag(name = "内置MCP服务器列表", description = "提供内置MCP服务器的静态响应接口")
public class BuiltInMcpServersController {

    private final McpToolDiscoveryService toolDiscoveryService;
    
    @Value("${SELF_HOST:}")
    private String selfHost;

    @GetMapping("/servers")
    @Operation(summary = "获取内置MCP服务器列表", description = "返回内置的MCP服务器配置列表")
    public Response<BuiltInMcpServersResponse> getBuiltInMcpServers(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer pageSize) {

        log.info("Getting built-in MCP servers: pageNum={}, pageSize={}", pageNum, pageSize);

        BuiltInMcpServersResponse response = new BuiltInMcpServersResponse();
        List<McpServerDto> servers = new ArrayList<>();

        // Trantor Meta MCP Server
        McpServerDto trantorMeta = createTrantorMetaMcpServer();
        servers.add(trantorMeta);

        // 设置响应数据
        response.setTotal((long) servers.size());
        response.setList(servers);

        return Response.ok(response);
    }

    private McpServerDto createTrantorMetaMcpServer() {
        McpServerDto server = new McpServerDto();
        server.setId("0c0a3425-1754-4495-8800-baff3000ce1f");
        server.setName("trantor-meta");
        server.setVersion("3.0.2506");
        server.setTransportType("sse");
        server.setDescription("Trantor is a low-code platform designed for building enterprise-grade backend systems. Its core components are Model, Service, and Scenario, which together enable structured application logic. At the heart of Trantor lies metadata (meta) — its most powerful abstraction, analogous to source code. Defined in JSON, this metadata drives the behavior of Models, Services, and Scenarios, enabling a fully metadata-driven architecture.");
        server.setInstruction("");
        server.setServerConfig("");
        server.setEndpoint("http://trantor2:8080/api/trantor/mcp/built-in/sse");
        server.setIsDefaultVersion(true);
        server.setIsPublished(true);

        // 动态获取实际的工具列表
        List<McpToolDto> tools = toolDiscoveryService.discoverTools();
        server.setTools(tools);
        return server;
    }
    
    @GetMapping("/get-internal-endpoint")
    @Operation(summary = "获取内部端点", description = "返回内置MCP的SSE端点地址")
    public Response<Map<String, String>> getInternalEndpoint(HttpServletRequest request) {
        String domain;
        
        if (selfHost != null && !selfHost.trim().isEmpty()) {
            domain = selfHost.trim();
            if (!domain.startsWith("http://") && !domain.startsWith("https://")) {
                domain = "http://" + domain;
            }
        } else {
            String scheme = request.getScheme();
            String serverName = request.getServerName();
            int serverPort = request.getServerPort();
            
            if ((scheme.equals("http") && serverPort == 80) || 
                (scheme.equals("https") && serverPort == 443)) {
                domain = scheme + "://" + serverName;
            } else {
                domain = scheme + "://" + serverName + ":" + serverPort;
            }
        }
        
        String endpoint = domain + "/api/trantor/mcp/built-in/sse";
        
        Map<String, String> result = new HashMap<>();
        result.put("endpoint", endpoint);
        
        log.info("Returning internal endpoint: {}", endpoint);
        
        return Response.ok(result);
    }
}
