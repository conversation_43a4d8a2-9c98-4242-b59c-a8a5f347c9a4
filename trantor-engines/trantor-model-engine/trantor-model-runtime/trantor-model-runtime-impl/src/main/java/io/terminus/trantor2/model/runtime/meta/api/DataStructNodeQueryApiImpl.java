package io.terminus.trantor2.model.runtime.meta.api;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.meta.request.QueryByKeyRequest;
import io.terminus.trantor2.meta.request.QueryByKeysRequest;
import io.terminus.trantor2.meta.request.SimpleModelInfoRequest;
import io.terminus.trantor2.model.common.api.DataStructNodeQueryApi;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.SimpleDataStructNode;
import io.terminus.trantor2.model.management.meta.repository.DataStructNodeRepo;
import io.terminus.trantor2.model.runtime.meta.service.DataStructNodeRuntimeService;
import io.terminus.trantor2.module.service.TeamService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/internal/trantor/portal/struct-node")
@RequiredArgsConstructor
public class DataStructNodeQueryApiImpl implements DataStructNodeQueryApi {
    private final DataStructNodeRuntimeService service;
    private final DataStructNodeRepo dataStructNodeRepo;
    private final TeamService teamService;
    private DataStructMetaCache modelMetaCache;

    @Override
    public <T extends Serializable> Response<DataStructNode> findByKeyAndTeam(String key, QueryByKeyRequest<T> request) {
        if (request != null && !ObjectUtils.isEmpty(request.getTeam())) {
            TrantorContext.setTeam(request.getTeam());
        }
        return Response.ok(service.findByKey(key).orElse(null));
    }

    @Override
    public <T extends Serializable> Response<Collection<DataStructNode>> findByKeys(QueryByKeysRequest<T> request) {
        return Response.ok(service.findByKeys(request));
    }

    @Override
    public <T extends Serializable> Response<Collection<SimpleDataStructNode>> listSimpleByKeys(SimpleModelInfoRequest<T> request) {
        String teamCode;
        if (request.getTeam() instanceof String) {
            teamCode = (String) request.getTeam();
        } else {
            teamCode = teamService.getTeamCode((Long) request.getTeam());
        }

        Collection<DataStructNode> models = dataStructNodeRepo.findAll(teamCode, request.getKeywords(), request.isExactMatch());
        List<SimpleDataStructNode> simpleModels = new ArrayList<>();
        if (CollectionUtils.isEmpty(models)) {
            return Response.ok(simpleModels);
        }

        for (DataStructNode model : models) {
            SimpleDataStructNode simpleModel = convertToSimpleDataStructNode(model, request.isIncludeSystemFields());

            // 递归填充关联模型元数据
            List<SimpleDataStructNode> relationModels = new ArrayList<>();
            Map<String, SimpleDataStructNode> queriedModelMap = new HashMap<>();
            queriedModelMap.put(model.getKey(), simpleModel);
            recursiveFillRelationModel(teamCode, simpleModel, relationModels, queriedModelMap, request.isIncludeSystemFields(), request.getCascadeDepth() - 1);
            simpleModel.setRelationModels(relationModels);

            simpleModels.add(simpleModel);
        }

        return Response.ok(simpleModels);
    }

    private SimpleDataStructNode convertToSimpleDataStructNode(DataStructNode model, boolean includeSystemFields) {
        SimpleDataStructNode simpleModel = new SimpleDataStructNode();
        simpleModel.setKey(model.getKey());
        simpleModel.setName(model.getName());
        simpleModel.setMainField(model.getProps().getMainFieldAlias());

        List<SimpleDataStructNode.SimpleDataStructFieldNode> simpleFields = new ArrayList<>();
        for (DataStructFieldNode field : model.getChildren()) {
            if (!includeSystemFields && BooleanUtils.isTrue(field.getProps().getIsSystemField())) {
                // 不包含系统字段时，忽略系统字段
                continue;
            }

            SimpleDataStructNode.SimpleDataStructFieldNode simpleField = getSimpleDataStructFieldNode(model.getTeamCode(), field);
            simpleFields.add(simpleField);
        }
        simpleModel.setFields(simpleFields);

        return simpleModel;
    }

    private void recursiveFillRelationModel(String teamCode, SimpleDataStructNode parentModel, List<SimpleDataStructNode> childrenModels, Map<String, SimpleDataStructNode> queriedModelMap, boolean includeSystemFields, int depth) {
        if (parentModel == null || depth <= 0) {
            return;
        }

        List<SimpleDataStructNode.SimpleDataStructFieldNode> fields = parentModel.getFields();
        if (CollectionUtils.isEmpty(fields)) {
            return;
        }

        // 获取当前模型的关联模型 alias 列表
        Set<String> relationModelKeys = new HashSet<>();
        List<SimpleDataStructNode> queriedChildren = new ArrayList<>();
        fields.forEach(field -> {
            if (StringUtils.isEmpty(field.getRelKey())) {
                return;
            }

            // 关联模型已经获取过元数据，不再重复获取，防止自关联时的无限递归
            if (queriedModelMap.containsKey(field.getRelKey())) {
                SimpleDataStructNode copied = new SimpleDataStructNode();
                SimpleDataStructNode queried = queriedModelMap.get(field.getRelKey());
                copied.setKey(queried.getKey());
                copied.setName(queried.getName());
                copied.setMainField(queried.getMainField());
                copied.setFields(queried.getFields());
                queriedChildren.add(copied);

                return;
            }

            relationModelKeys.add(field.getRelKey());
        });

        if (CollectionUtils.isNotEmpty(relationModelKeys)) {
            QueryByKeysRequest<String> queryByKeysRequest = new QueryByKeysRequest<>();
            queryByKeysRequest.setTeam(teamCode);
            queryByKeysRequest.setKeys(new ArrayList<>(relationModelKeys));
            // 获取当前模型的关联模型元信息
            Collection<DataStructNode> relationModels = service.findByKeys(queryByKeysRequest);
            List<SimpleDataStructNode> simpleRelationModels = relationModels.stream()
                    .map(it -> convertToSimpleDataStructNode(it, includeSystemFields))
                    .collect(Collectors.toList());
            simpleRelationModels.forEach(it -> queriedModelMap.put(it.getKey(), it));

            queriedChildren.addAll(simpleRelationModels);
        }

        if (CollectionUtils.isEmpty(queriedChildren)) {
            return;
        }

        for (SimpleDataStructNode childModel : queriedChildren) {
            List<SimpleDataStructNode> children = new ArrayList<>();
            recursiveFillRelationModel(teamCode, childModel, children, queriedModelMap, includeSystemFields, depth-1);

            childModel.setRelationModels(children);
        }
        childrenModels.addAll(queriedChildren);
    }

    @NotNull
    private SimpleDataStructNode.SimpleDataStructFieldNode getSimpleDataStructFieldNode(String teamCode, DataStructFieldNode field) {
        SimpleDataStructNode.SimpleDataStructFieldNode simpleField = new SimpleDataStructNode.SimpleDataStructFieldNode();
        simpleField.setKey(field.getKey());
        simpleField.setName(field.getName());
        simpleField.setAlias(field.getAlias());
        simpleField.setType(field.getProps().getFieldType().name());
        simpleField.setRequired(field.getProps().isRequired());

        if (field.getProps().getRelationMeta() != null) {
            simpleField.setRelKey(field.getProps().getRelationMeta().getRelationModelAlias());
            DataStructNode relationModel = modelMetaCache.getModelMeta(teamCode, field.getProps().getRelationMeta().getRelationModelAlias());
            if (relationModel != null) {
                simpleField.setRelName(relationModel.getName());
            }
            simpleField.setRelType(field.getProps().getRelationMeta().getRelationType().name());
            simpleField.setSync(field.getProps().getRelationMeta().isSync());
        }
        return simpleField;
    }
}
