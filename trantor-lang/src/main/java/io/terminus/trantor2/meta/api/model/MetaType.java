package io.terminus.trantor2.meta.api.model;

import cn.hutool.core.util.BooleanUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static io.terminus.trantor2.meta.resource.ext.ExtNodeMeta.EXT_TYPE;


/**
 * <AUTHOR>
 * 2023/6/6 3:59 PM
 */
@Getter
public enum MetaType {
    Root("根节点"),
    @Deprecated // use MenuTree instead
    MenuRoot("菜单根节点"),
    Module("模块", true),
    ModuleSourceLib("模块资源库"),
    ExtModule("扩展模块"),
    Ext("扩展资源"),
    ExtNode("全新的扩展资源"), // 实际持久化的 type 为 Ext_ + 类型, 如: Ext_Scene
    ModuleVariable("模块变量"),
    PromptVariable("提示词变量"),
    FolderRoot("文件夹根节点"),
    @Deprecated
    ErrorCodeRoot("错误码根节点"),
    @Deprecated
    TakeCodeRoot("取号规则根节点"),
    @Deprecated
    StateConfigRoot("状态根节点"),
    Model("模型", true),
    @Deprecated
    Flow("业务流"),
    Process("业务流"),
    @Deprecated
    Event("操作事件"),
    @Deprecated
    StateConfig("状态配置"),
    Action("扩展服务", true),
    @Deprecated
    TakeCode("取号规则"),
    ErrorCode("异常码", true),
    @Deprecated
    Menu("菜单"),
    MenuTree("菜单树"),
    Scene("页面场景", true),
    View("页面", true),
    Validation("校验规则"),
    ServiceDefinition("编排服务", true),
    AIAgent("智能体", false),
    AIDigitalStaff("数智员工", false),
    KnowledgeBase("知识库", false),
    Folder("文件夹", true),
    WorkflowGroup("审批流场景", true),
    ConnectorInst("连接器实例"),
    PortalIcon("门户图标"),
    MenuIcon("菜单图标"),
    EventDefinition("通知事件"),
    Trigger("触发器"),
    NoticeScene("通知场景", true),
    Permission("权限项", true),
    PermissionView("权限授权视图", true),
    DataCondition("数据规则", true),
    DataControlDimension("数据控权维度", true),
    ImportExportTemplate("导入导出场景", true),
    PrintScene("打印场景", true),
    Api("应用程序接口"),
    Overview("产品概念"),
    OverviewProcess("产品概念流程");
    ;
    private final String label;
    private final Boolean extendable;

    public Boolean getExtendable() {
        return BooleanUtil.isTrue(extendable);
    }

    MetaType(String label) {
        this.label = label;
        this.extendable = false;
    }

    MetaType(String label, Boolean extendable) {
        this.label = label;
        this.extendable = extendable;
    }

    public static Set<String> getIndexableTypes() {
        return ImmutableSet.<String>builder().addAll(getSearchableTypes()).add(
                MetaType.View.name(),
                MetaType.TakeCode.name(),
                MetaType.ErrorCode.name(),
                MetaType.Validation.name(),
                MetaType.Module.name(),
                MetaType.ModuleSourceLib.name(),
                MetaType.ModuleVariable.name(),
                MetaType.PromptVariable.name(),
                MetaType.Folder.name(),
                MetaType.MenuTree.name(),
                MetaType.Api.name()
        ).build();
    }

    public static List<String> getSearchableTypes() {
        return ImmutableList.of(
                MetaType.Scene.name(),
                MetaType.Model.name(),
                MetaType.ServiceDefinition.name(),
                MetaType.Action.name(),
                MetaType.Flow.name(),
                MetaType.Process.name(),
                MetaType.WorkflowGroup.name(),
                MetaType.EventDefinition.name(),
                MetaType.Trigger.name(),
                MetaType.NoticeScene.name(),
                MetaType.ImportExportTemplate.name(),
                MetaType.PrintScene.name(),
                MetaType.Permission.name(),
                MetaType.DataCondition.name(),
                MetaType.DataControlDimension.name(),
                MetaType.OverviewProcess.name(),
                MetaType.AIAgent.name(),
                MetaType.KnowledgeBase.name()
        );
    }

    public static List<String> getStatisticalTypes() {
        return ImmutableList.of(
                MetaType.Scene.name(),
                MetaType.View.name(),
                MetaType.Model.name(),
                MetaType.ServiceDefinition.name(),
                MetaType.Action.name(),
                MetaType.Trigger.name(),
                MetaType.WorkflowGroup.name(),
                MetaType.EventDefinition.name(),
                MetaType.NoticeScene.name(),
                MetaType.ImportExportTemplate.name(),
                MetaType.ErrorCode.name(),
                MetaType.PrintScene.name(),
                MetaType.Permission.name(),
                MetaType.DataCondition.name(),
                MetaType.DataControlDimension.name()
        );
    }


    public static List<String> getExtendNodeTypes() {
        return Arrays.stream(values()).filter(MetaType::getExtendable)
                .map(it -> EXT_TYPE + it.name()).collect(Collectors.toList());
    }
}
