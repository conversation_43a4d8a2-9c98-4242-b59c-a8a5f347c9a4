package io.terminus.trantor2.scene.config.datamanager;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.scene.config.SceneConfig;
import io.terminus.trantor2.scene.config.SceneTemplateConfig;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.i18n.ViewI18nConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.util.CollectionUtils;

import java.util.*;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@Schema(description = "数据管理场景配置")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@FieldNameConstants
public class DataManagerSceneConfig implements SceneConfig {
    private static final long serialVersionUID = -3656194487915214359L;

    @Schema(description = "模版 ID")
    @Deprecated
    private Long templateId;
    @Schema(description = "模版配置")
    private SceneTemplateConfig templateConfig;
    @Schema(description = "前端使用到的模型 alias 集合")
    private Set<String> usedModelAlias = new HashSet<>();
    @Schema(description = "视图配置")
    private List<DataManagerView> views = new ArrayList<>();
    @Schema(description = "i18n 配置")
    private ViewI18nConfig i18nConfig;

    @Schema(description = "场景内容版本号（用于触发场景自身变更，驱动 modifiedAt 更新）")
    private Long contentVersion;

    @Override
    public SceneType getType() {
        return SceneType.DATA;
    }

    @Override
    @JsonIgnore
    public Set<String> getI18nKeySet() {
        return Optional.ofNullable(i18nConfig).map(ViewI18nConfig::getI18nKeySet).orElse(Collections.emptySet());
    }

    public void addView(DataManagerView view) {
        if (view == null) {
            return;
        }
        if (CollectionUtils.isEmpty(views)) {
            views = new ArrayList<>();
        }
        views.removeIf(it -> Objects.equals(it.getKey(), view.getKey()));
        views.add(view);
    }

    public DataManagerView getView(String key) {
        if (CollectionUtils.isEmpty(views)) {
            throw viewNotFound(key);
        }
        Optional<DataManagerView> configOpt = views.stream()
            .filter(config -> key.equals(config.getKey())).findFirst();
        return configOpt.orElseThrow(() -> viewNotFound(key));
    }

    private ValidationException viewNotFound(String key) {
        return new ValidationException("view [" + key + "] not found");
    }
}
