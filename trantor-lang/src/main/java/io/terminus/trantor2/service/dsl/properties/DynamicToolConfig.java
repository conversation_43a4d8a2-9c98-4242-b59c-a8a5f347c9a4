package io.terminus.trantor2.service.dsl.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 动态工具配置
 * agent 工具分为两种：1. 固定工具 2. 动态工具
 * 1. 固定工具：在 agent 配置时指定，后续 agent 初始化时将固定工具添加至 tools 中
 * 2. 动态工具: 在 agent 配置时指定范围，agent 运行时基于用户输入动态向量匹配，匹配成功后将工具追加至 tools 中(存在固定工具与动态工具并存)
 */
@Data
@Schema(description = "动态工具配置")
public class DynamicToolConfig implements Serializable {
    @Schema(description = "工具范围类型，枚举值: (TEAM)项目范围/(MODULE)模块范围/(CUSTOM)自定义范围")
    private DynamicToolScope scope;

    @Schema(description = "scope 为自定义范围时，指定的自定义工具范围")
    private List<SkillTool> customTools;
}
