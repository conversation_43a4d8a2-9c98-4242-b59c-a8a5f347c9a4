package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.ReasoningType;
import lombok.Getter;
import lombok.Setter;

/**
 * LlmModelSetting
 *
 * <AUTHOR> Created on 2025/3/31 16:56
 */
@Setter
@Getter
public class LlmModelSetting {

    private Double frequencyPenalty = 0.0;
    private Double presencePenalty = 0.0;
    private Double temperature = 0.0;
    private Integer topP = 1;

    // 输入设置，携带会话轮次
    private Integer chatRounds = 5;

    // 输出设置
    private Integer maxTokens = 4096;

    /**
     * 推理类型
     */
    @Deprecated
    private ReasoningType reasoningType;
}
