package io.terminus.trantor2.service.dsl.properties;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.service.dsl.enums.AIConversationReferLevel;
import io.terminus.trantor2.service.dsl.enums.LlmModelType;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AIProperties extends AbstractProperties {
    private static final long serialVersionUID = 691800413951985814L;

    /**
     * 大模型配置，与Agent保持一致
     */
    private LlmModel model;
    /**
     * 大语言模型类型Key，后续新版本全都用providerType属性代替，这里保留做兼容处理
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Deprecated
    private String aiModelKey;
    /**
     * 模型供应商类型
     */
    @Deprecated
    private String providerType;
    /**
     * 模型开发商
     */
    @Deprecated
    private String modelPublisher;
    /**
     * 模型名称
     */
    @Deprecated
    private String modelName;
    /**
     * 模型类型
     */
    @Deprecated
    private String modelType;
    /**
     * 模型抽样随机创造性温度
     */
    @Deprecated
    private Double temperature;
    /**
     * token最大长度
     */
    @Deprecated
    private Integer maxTokens;
    /**
     * 系统内容
     */
    private String systemMessage;
    /**
     * 系统内容(英文)
     */
    private String systemMessageEn;
    /**
     * 用户prompt编号
     */
    private String userPromptCode;
    /**
     * 用户内容
     */
    private String userMessage;
    /**
     * 用户内容(英文)
     */
    private String userMessageEn;
    /**
     * 系统内容宏变量占位符
     */
    private List<StringEntry> sysMessagePlaceholderMapping;
    /**
     * 用户内容宏变量占位符
     */
    private List<StringEntry> userMessagePlaceholderMapping;
    /**
     * 技能工具
     */
    private List<SkillTool> skillTools;
    /**
     * 是否stream模式
     */
    private Boolean stream = false;
    /**
     * 附件地址
     */
    private VarValue attachments;
    /**
     * 多轮对话引用级别（默认：节点级）
     */
    @Deprecated
    private AIConversationReferLevel conversationReferLevel = AIConversationReferLevel.Node;
    /**
     * 保留历史对话记录
     */
    private Boolean conversationHistory = false;
    /**
     * 视觉识别
     */
    private Boolean visualUnderstanding = false;
    /**
     * 返回格式
     */
    private String responseFormat;
    /**
     * 图片尺寸
     */
    private String size;
    /**
     * 图片质量
     */
    private String quality;
    /**
     * 音频文件地址路径
     */
    private String audioPath;
    /**
     * 音频文件地址路径宏变量占位符
     */
    private List<StringEntry> audioPathPlaceholderMapping;
    /**
     * 输出参数定义
     */
    private List<Field> output;
    /**
     * 出参赋值
     */
    private OutputAssign outputAssign;

    public String getModelPublisher() {
        if (Objects.nonNull(model)) {
            return model.getModelPublisher();
        } else {
            return modelPublisher;
        }
    }

    public String getModelName() {
        if (Objects.nonNull(model)) {
            return model.getName();
        } else {
            return modelName;
        }
    }

    public String getModelType() {
        if (Objects.nonNull(model)) {
            return model.getType();
        } else {
            return modelType;
        }
    }

    public Double getTemperature() {
        if (Objects.nonNull(model)) {
            return model.getSetting().getTemperature();
        } else {
            return temperature;
        }
    }

    public Integer getMaxTokens() {
        if (Objects.nonNull(model)) {
            return model.getSetting().getMaxTokens();
        } else {
            return maxTokens;
        }
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        if (StringUtils.isBlank(getModelType())) {
            errorContext.addErrorInfo("模型类型未配置");
        }

        LlmModelType currentModelType = EnumUtils.getEnum(LlmModelType.class, modelType.toUpperCase());
        switch (currentModelType) {
            case TEXT_GENERATION:
            case EMBEDDING:
                if (StringUtils.isBlank(systemMessage)) {
                    errorContext.addErrorInfo("系统内容未配置");
                }

                if (sysMessagePlaceholderMapping != null) {
                    errorContext.addTag("系统内容占位符");
                    sysMessagePlaceholderMapping.forEach(p -> p.validate(errorContext));
                    errorContext.removeTag();
                }
                break;
            default:
                break;
        }

        if (StringUtils.isBlank(userMessage)) {
            errorContext.addErrorInfo("用户内容未配置");
        }

        if (userMessagePlaceholderMapping != null) {
            errorContext.addTag("用户内容占位符");
            userMessagePlaceholderMapping.forEach(p -> p.validate(errorContext));
            errorContext.removeTag();
        }

        if (output != null && !output.isEmpty()) {
            errorContext.addTag("LLM 出参");
            output.forEach(o -> o.validate(errorContext));
            errorContext.removeTag();

            if (outputAssign != null && outputAssign.isSystemDefault()) {
                errorContext.getGlobalVariable()
                        .addElement(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            } else {
                errorContext.addVariable(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            }
        }

        if (outputAssign != null) {
            errorContext.addTag("出参赋值");
            outputAssign.validate(errorContext);
            errorContext.removeTag();
        }
    }
}
