package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.BaseElement;
import lombok.Getter;
import lombok.Setter;

/**
 * Tool
 *
 * <AUTHOR> Created on 2025/4/10 04:04
 */
@Setter
@Getter
public class Tool extends BaseElement {
    private static final long serialVersionUID = -2363275867573745270L;

    protected String key;
    protected String name;
    protected String desc;
}
