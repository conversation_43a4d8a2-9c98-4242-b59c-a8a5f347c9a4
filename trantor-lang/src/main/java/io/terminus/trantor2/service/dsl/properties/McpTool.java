package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.ToolType;
import io.terminus.trantor2.service.dsl.properties.ai.ToolVisible;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
public class McpTool extends SkillTool {
    private static final long serialVersionUID = 1312581228263749697L;

    private String instructions;
    private String mcpServerEndpoint;
    private String mcpServerVersion;
    private List<StringEntry> mcpServerArgs;
    private String modelName;
    private String modelPublisher;

    /**
     * 源ID
     */
    private String sourceId;

    /**
     * MCP工具的的类型，ai-proxy, built-in, third-party
     */
    private String sourceType;

    /**
     * 提供者
     */
    private String provider;

    /**
     * 工具包中的子工具
     */
    private List<SubTool> tools;

    @Setter
    @Getter
    public static class SubTool extends Tool {
        private static final long serialVersionUID = 1L;

        private List<Field> input;

        /**
         * 当为true时，该工具的结果会直接作为最终输出结果
         */
        private boolean finalOutput;

        private ToolVisible toolVisible;

        /**
         * 当为true时，该工具调用后会立即返回结果
         */
        private boolean immediatelyOutput;

        private String aliasName;
    }

    public McpTool(String toolKey) {
        this.key = toolKey;
        this.type = ToolType.Mcp;
    }
}
