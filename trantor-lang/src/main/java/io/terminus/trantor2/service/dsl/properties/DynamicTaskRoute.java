package io.terminus.trantor2.service.dsl.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 动态任务路由：将某类任务映射到一组候选模型（包含优先级）
 */
@Getter
@Setter
@Schema(title = "动态任务路由", description = "任务类型到候选模型映射")
public class DynamicTaskRoute {

    @Schema(title = "任务类型", description = "例如：成本低、成本高、多模态、复杂推理等")
    private String taskType;

    @Schema(title = "候选模型列表", description = "按优先级从高到低排列")
    private List<LlmModel> candidateModels = new ArrayList<>(2);

}
