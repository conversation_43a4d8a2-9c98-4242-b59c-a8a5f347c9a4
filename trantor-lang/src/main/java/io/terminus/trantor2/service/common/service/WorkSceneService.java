package io.terminus.trantor2.service.common.service;

import io.terminus.trantor2.service.common.meta.WorkSceneMeta;
import io.terminus.trantor2.service.common.enums.WorkSceneType;
import io.terminus.trantor2.service.common.enums.WorkSceneStatus;

import java.util.List;
import java.util.Map;

/**
 * 工作场景管理服务接口
 *
 * <AUTHOR> Created on 2025/9/4
 */
public interface WorkSceneService {

    /**
     * 创建工作场景
     *
     * @param sceneMeta 场景元数据
     * @return 场景ID
     */
    String createWorkScene(WorkSceneMeta sceneMeta);

    /**
     * 更新工作场景
     *
     * @param sceneId 场景ID
     * @param sceneMeta 场景元数据
     * @return 是否更新成功
     */
    boolean updateWorkScene(String sceneId, WorkSceneMeta sceneMeta);

    /**
     * 删除工作场景
     *
     * @param sceneId 场景ID
     * @return 是否删除成功
     */
    boolean deleteWorkScene(String sceneId);

    /**
     * 根据ID查询场景
     *
     * @param sceneId 场景ID
     * @return 场景元数据
     */
    WorkSceneMeta getWorkSceneById(String sceneId);

    /**
     * 根据类型查询场景
     *
     * @param sceneType 场景类型
     * @return 场景列表
     */
    List<WorkSceneMeta> getWorkScenesByType(WorkSceneType sceneType);

    /**
     * 根据状态查询场景
     *
     * @param status 场景状态
     * @return 场景列表
     */
    List<WorkSceneMeta> getWorkScenesByStatus(WorkSceneStatus status);

    /**
     * 查询数字员工参与的场景
     *
     * @param staffId 数字员工ID
     * @return 场景列表
     */
    List<WorkSceneMeta> getWorkScenesByStaffId(String staffId);

    /**
     * 启动工作场景
     *
     * @param sceneId 场景ID
     * @param parameters 启动参数
     * @return 执行结果
     */
    SceneExecutionResult startWorkScene(String sceneId, Map<String, Object> parameters);

    /**
     * 停止工作场景
     *
     * @param sceneId 场景ID
     * @return 是否停止成功
     */
    boolean stopWorkScene(String sceneId);

    /**
     * 暂停工作场景
     *
     * @param sceneId 场景ID
     * @return 是否暂停成功
     */
    boolean pauseWorkScene(String sceneId);

    /**
     * 恢复工作场景
     *
     * @param sceneId 场景ID
     * @return 是否恢复成功
     */
    boolean resumeWorkScene(String sceneId);

    /**
     * 获取场景执行状态
     *
     * @param sceneId 场景ID
     * @return 执行状态
     */
    SceneExecutionStatus getExecutionStatus(String sceneId);

    /**
     * 获取场景执行历史
     *
     * @param sceneId 场景ID
     * @param limit 限制数量
     * @return 执行历史列表
     */
    List<SceneExecutionHistory> getExecutionHistory(String sceneId, int limit);

    /**
     * 验证场景配置
     *
     * @param sceneMeta 场景元数据
     * @return 验证结果
     */
    ValidationResult validateWorkScene(WorkSceneMeta sceneMeta);

    /**
     * 复制工作场景
     *
     * @param sourceSceneId 源场景ID
     * @param newSceneName 新场景名称
     * @return 新场景ID
     */
    String cloneWorkScene(String sourceSceneId, String newSceneName);

    /**
     * 导出场景配置
     *
     * @param sceneId 场景ID
     * @return 场景配置JSON
     */
    String exportWorkScene(String sceneId);

    /**
     * 导入场景配置
     *
     * @param sceneConfig 场景配置JSON
     * @return 场景ID
     */
    String importWorkScene(String sceneConfig);

    /**
     * 场景执行结果
     */
    class SceneExecutionResult {
        private String executionId;
        private boolean success;
        private String message;
        private Map<String, Object> result;
        private long startTime;
        private long endTime;

        // getters and setters
        public String getExecutionId() { return executionId; }
        public void setExecutionId(String executionId) { this.executionId = executionId; }
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Map<String, Object> getResult() { return result; }
        public void setResult(Map<String, Object> result) { this.result = result; }
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
    }

    /**
     * 场景执行状态
     */
    class SceneExecutionStatus {
        private String sceneId;
        private String executionId;
        private WorkSceneStatus status;
        private int progress;
        private String currentStep;
        private Map<String, Object> stepResults;
        private List<String> errors;
        private long startTime;
        private long lastUpdateTime;

        // getters and setters
        public String getSceneId() { return sceneId; }
        public void setSceneId(String sceneId) { this.sceneId = sceneId; }
        public String getExecutionId() { return executionId; }
        public void setExecutionId(String executionId) { this.executionId = executionId; }
        public WorkSceneStatus getStatus() { return status; }
        public void setStatus(WorkSceneStatus status) { this.status = status; }
        public int getProgress() { return progress; }
        public void setProgress(int progress) { this.progress = progress; }
        public String getCurrentStep() { return currentStep; }
        public void setCurrentStep(String currentStep) { this.currentStep = currentStep; }
        public Map<String, Object> getStepResults() { return stepResults; }
        public void setStepResults(Map<String, Object> stepResults) { this.stepResults = stepResults; }
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getLastUpdateTime() { return lastUpdateTime; }
        public void setLastUpdateTime(long lastUpdateTime) { this.lastUpdateTime = lastUpdateTime; }
    }

    /**
     * 场景执行历史
     */
    class SceneExecutionHistory {
        private String executionId;
        private String sceneId;
        private WorkSceneStatus status;
        private long startTime;
        private long endTime;
        private long duration;
        private boolean success;
        private String errorMessage;
        private Map<String, Object> parameters;
        private Map<String, Object> result;

        // getters and setters
        public String getExecutionId() { return executionId; }
        public void setExecutionId(String executionId) { this.executionId = executionId; }
        public String getSceneId() { return sceneId; }
        public void setSceneId(String sceneId) { this.sceneId = sceneId; }
        public WorkSceneStatus getStatus() { return status; }
        public void setStatus(WorkSceneStatus status) { this.status = status; }
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
        public Map<String, Object> getResult() { return result; }
        public void setResult(Map<String, Object> result) { this.result = result; }
    }

    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private List<String> errors;
        private List<String> warnings;

        // getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
        public List<String> getWarnings() { return warnings; }
        public void setWarnings(List<String> warnings) { this.warnings = warnings; }
    }
}
