package io.terminus.trantor2.service.common.meta;

import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.service.common.enums.WorkSceneType;
import io.terminus.trantor2.service.common.enums.WorkSceneStatus;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 工作场景元数据
 * 用于定义和管理数字员工的工作场景，包含多个数字员工及其关系
 *
 * <AUTHOR> Created on 2025/9/4
 */
@Setter
@Getter
public class WorkSceneMeta extends ModuleBaseMeta<WorkSceneMeta.Props> {
    private static final long serialVersionUID = 7371081674553617875L;

    @Override
    public MetaType getMetaType() {
        return MetaType.WorkScene;
    }

    @Data
    public static class Props implements ResourceProps {
        /**
         * 工作场景类型
         */
        private WorkSceneType sceneType;

        /**
         * 场景状态
         */
        private WorkSceneStatus status;

        /**
         * 参与的数字员工列表
         */
        private List<String> participantStaffIds;

        /**
         * 数字员工关系列表
         */
        private List<String> relationIds;

        /**
         * 场景配置
         */
        private WorkSceneConfig sceneConfig;

        /**
         * 是否启用该场景
         */
        private Boolean isEnabled = true;

        /**
         * 场景优先级
         */
        private Integer priority = 0;

        /**
         * 场景标签
         */
        private List<String> tags;
    }

    /**
     * 工作场景配置
     */
    @Data
    public static class WorkSceneConfig {
        /**
         * 场景执行策略
         */
        private ExecutionStrategy executionStrategy;

        /**
         * 监控配置
         */
        private MonitoringConfig monitoringConfig;

        /**
         * 异常处理配置
         */
        private ExceptionHandlingConfig exceptionHandlingConfig;

        /**
         * 性能配置
         */
        private PerformanceConfig performanceConfig;

        /**
         * 自定义配置参数
         */
        private Map<String, Object> customParams;
    }

    /**
     * 执行策略配置
     */
    @Data
    public static class ExecutionStrategy {
        /**
         * 执行模式（串行/并行/混合）
         */
        private String executionMode = "SEQUENTIAL";

        /**
         * 最大并发数
         */
        private Integer maxConcurrency = 5;

        /**
         * 超时时间（分钟）
         */
        private Integer timeoutMinutes = 60;

        /**
         * 重试策略
         */
        private RetryStrategy retryStrategy;

        /**
         * 是否允许部分失败
         */
        private Boolean allowPartialFailure = false;
    }

    /**
     * 重试策略
     */
    @Data
    public static class RetryStrategy {
        /**
         * 最大重试次数
         */
        private Integer maxRetries = 3;

        /**
         * 重试间隔（秒）
         */
        private Integer retryIntervalSeconds = 30;

        /**
         * 重试策略类型（固定间隔/指数退避）
         */
        private String retryType = "FIXED_INTERVAL";

        /**
         * 退避倍数（用于指数退避）
         */
        private Double backoffMultiplier = 2.0;
    }

    /**
     * 监控配置
     */
    @Data
    public static class MonitoringConfig {
        /**
         * 是否启用监控
         */
        private Boolean enabled = true;

        /**
         * 监控指标
         */
        private List<String> metrics;

        /**
         * 告警阈值
         */
        private Map<String, Object> alertThresholds;

        /**
         * 监控频率（秒）
         */
        private Integer monitoringIntervalSeconds = 60;

        /**
         * 日志级别
         */
        private String logLevel = "INFO";
    }

    /**
     * 异常处理配置
     */
    @Data
    public static class ExceptionHandlingConfig {
        /**
         * 异常处理策略
         */
        private String strategy = "FAIL_FAST";

        /**
         * 异常通知配置
         */
        private List<String> notificationChannels;

        /**
         * 自动恢复配置
         */
        private AutoRecoveryConfig autoRecoveryConfig;

        /**
         * 异常忽略规则
         */
        private List<String> ignoreRules;
    }

    /**
     * 自动恢复配置
     */
    @Data
    public static class AutoRecoveryConfig {
        /**
         * 是否启用自动恢复
         */
        private Boolean enabled = false;

        /**
         * 恢复策略
         */
        private String recoveryStrategy = "RESTART";

        /**
         * 最大恢复尝试次数
         */
        private Integer maxRecoveryAttempts = 3;

        /**
         * 恢复间隔（分钟）
         */
        private Integer recoveryIntervalMinutes = 5;
    }

    /**
     * 性能配置
     */
    @Data
    public static class PerformanceConfig {
        /**
         * 最大内存使用量（MB）
         */
        private Integer maxMemoryMB = 1024;

        /**
         * 最大CPU使用率（百分比）
         */
        private Integer maxCpuPercent = 80;

        /**
         * 资源限制策略
         */
        private String resourceLimitStrategy = "THROTTLE";

        /**
         * 性能优化选项
         */
        private Map<String, Object> optimizationOptions;
    }
}
