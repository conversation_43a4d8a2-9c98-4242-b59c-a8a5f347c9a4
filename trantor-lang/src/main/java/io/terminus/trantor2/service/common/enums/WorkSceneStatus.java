package io.terminus.trantor2.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工作场景状态枚举
 *
 * <AUTHOR> Created on 2025/9/4
 */
@AllArgsConstructor
@Getter
public enum WorkSceneStatus {

    /**
     * 草稿状态 - 场景正在设计中，尚未完成
     */
    DRAFT("草稿"),

    /**
     * 就绪状态 - 场景已配置完成，可以启动
     */
    READY("就绪"),

    /**
     * 运行中 - 场景正在执行
     */
    RUNNING("运行中"),

    /**
     * 暂停状态 - 场景已暂停执行
     */
    PAUSED("暂停"),

    /**
     * 已完成 - 场景执行完成
     */
    COMPLETED("已完成"),

    /**
     * 失败状态 - 场景执行失败
     */
    FAILED("失败"),

    /**
     * 已停止 - 场景被手动停止
     */
    STOPPED("已停止"),

    /**
     * 维护中 - 场景正在维护，暂时不可用
     */
    MAINTENANCE("维护中");

    private final String display;
}
