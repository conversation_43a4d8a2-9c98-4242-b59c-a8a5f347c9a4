package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.BaseElement;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.List;

/**
 * Skill
 *
 * <AUTHOR> Created on 2025/8/4 16:20
 */
@Setter
@Getter
public class Skill extends BaseElement {

    @Serial
    private static final long serialVersionUID = -6683489834877800990L;

    /**
     * 技能名称
     */
    private String name;

    /**
     * 技能描述
     */
    private String desc;

    /**
     * 技能工具
     */
    private List<SkillTool> skillTools;

}
