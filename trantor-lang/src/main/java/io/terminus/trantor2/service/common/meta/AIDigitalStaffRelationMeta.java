package io.terminus.trantor2.service.common.meta;

import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.service.common.enums.DigitalStaffRelationType;
import io.terminus.trantor2.service.common.enums.DigitalStaffRelationDirection;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 数字员工关系元数据
 * 用于表达和维护数字员工之间的工作关系
 *
 * <AUTHOR> Created on 2025/9/4
 */
@Setter
@Getter
public class AIDigitalStaffRelationMeta extends ModuleBaseMeta<AIDigitalStaffRelationMeta.Props> {
    private static final long serialVersionUID = 7371081674553617874L;

    @Override
    public MetaType getMetaType() {
        return MetaType.AIDigitalStaffRelation;
    }

    @Data
    public static class Props implements ResourceProps {
        /**
         * 关系类型
         */
        private DigitalStaffRelationType relationType;

        /**
         * 关系方向（单向/双向）
         */
        private DigitalStaffRelationDirection direction;

        /**
         * 源数字员工ID列表（发起方）
         */
        private List<String> sourceStaffIds;

        /**
         * 目标数字员工ID列表（接收方）
         */
        private List<String> targetStaffIds;

        /**
         * 关系配置参数
         */
        private RelationConfig relationConfig;

        /**
         * 是否启用该关系
         */
        private Boolean isEnabled = true;

        /**
         * 关系描述
         */
        private String relationDescription;

        /**
         * 关系优先级（用于多重关系时的排序）
         */
        private Integer priority = 0;
    }

    /**
     * 关系配置
     */
    @Data
    public static class RelationConfig {
        /**
         * 交接配置（适用于交接关系）
         */
        private HandoverConfig handoverConfig;

        /**
         * 汇报配置（适用于汇报关系）
         */
        private ReportConfig reportConfig;

        /**
         * 协商配置（适用于协商关系）
         */
        private ConsultationConfig consultationConfig;

        /**
         * 汇集配置（适用于汇集关系）
         */
        private AggregationConfig aggregationConfig;

        /**
         * 通用配置参数
         */
        private Map<String, Object> customParams;
    }

    /**
     * 交接配置
     */
    @Data
    public static class HandoverConfig {
        /**
         * 交接触发条件
         */
        private String triggerCondition;

        /**
         * 交接数据类型
         */
        private List<String> dataTypes;

        /**
         * 是否需要确认
         */
        private Boolean requireConfirmation = false;

        /**
         * 超时时间（分钟）
         */
        private Integer timeoutMinutes;

        /**
         * 失败重试次数
         */
        private Integer retryCount = 3;
    }

    /**
     * 汇报配置
     */
    @Data
    public static class ReportConfig {
        /**
         * 汇报频率（cron表达式）
         */
        private String reportFrequency;

        /**
         * 汇报内容模板
         */
        private String reportTemplate;

        /**
         * 汇报数据范围
         */
        private List<String> dataScope;

        /**
         * 是否实时汇报
         */
        private Boolean realTimeReport = false;

        /**
         * 汇报格式
         */
        private String reportFormat = "JSON";
    }

    /**
     * 协商配置
     */
    @Data
    public static class ConsultationConfig {
        /**
         * 协商主题
         */
        private String consultationTopic;

        /**
         * 协商规则
         */
        private List<String> consultationRules;

        /**
         * 决策算法
         */
        private String decisionAlgorithm;

        /**
         * 最大协商轮次
         */
        private Integer maxRounds = 5;

        /**
         * 协商超时时间（分钟）
         */
        private Integer timeoutMinutes = 30;

        /**
         * 是否需要人工介入
         */
        private Boolean requireHumanIntervention = false;
    }

    /**
     * 汇集配置
     */
    @Data
    public static class AggregationConfig {
        /**
         * 汇集策略
         */
        private String aggregationStrategy;

        /**
         * 数据合并规则
         */
        private List<String> mergeRules;

        /**
         * 冲突解决策略
         */
        private String conflictResolutionStrategy;

        /**
         * 汇集触发条件
         */
        private String triggerCondition;

        /**
         * 最大等待时间（分钟）
         */
        private Integer maxWaitMinutes = 60;

        /**
         * 最小参与者数量
         */
        private Integer minParticipants = 2;
    }
}
