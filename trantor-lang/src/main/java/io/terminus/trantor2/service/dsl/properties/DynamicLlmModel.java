package io.terminus.trantor2.service.dsl.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 动态模型配置
 */
@Getter
@Setter
@Schema(title = "动态模型配置", description = "根据策略动态选择调用模型")
public class DynamicLlmModel {

    @Schema(title = "调优机制", description = "模型动态调优机制：按性能/按成本/按能力/自定义")
    private Mode mode = Mode.PERFORMANCE_FIRST;

    @Schema(title = "任务路由", description = "任务类型到候选模型的映射")
    private List<DynamicTaskRoute> routes = new ArrayList<>(2);

    /**
     * 动态选择模式
     */
    public enum Mode {
        /** 按性能优先 */
        PERFORMANCE_FIRST,
        /** 按成本优先 */
        COST_FIRST,
        /** 按能力优先（如多模态、复杂推理等） */
        CAPABILITY_FIRST,
        /** 自定义 */
        CUSTOM
    }
}
