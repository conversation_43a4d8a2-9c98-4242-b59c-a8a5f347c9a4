package io.terminus.trantor2.service.common.util;

import io.terminus.trantor2.service.common.meta.AIDigitalStaffRelationMeta;
import io.terminus.trantor2.service.common.enums.DigitalStaffRelationType;
import io.terminus.trantor2.service.common.enums.DigitalStaffRelationDirection;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 数字员工关系构建器工具类
 * 提供便捷的方法来创建各种类型的数字员工关系
 *
 * <AUTHOR> Created on 2025/9/4
 */
public class DigitalStaffRelationBuilder {

    /**
     * 创建独立完成关系
     *
     * @param staffId 数字员工ID
     * @param description 关系描述
     * @return 关系元数据
     */
    public static AIDigitalStaffRelationMeta createIndependentRelation(String staffId, String description) {
        AIDigitalStaffRelationMeta meta = new AIDigitalStaffRelationMeta();
        AIDigitalStaffRelationMeta.Props props = new AIDigitalStaffRelationMeta.Props();
        
        props.setRelationType(DigitalStaffRelationType.INDEPENDENT);
        props.setDirection(DigitalStaffRelationDirection.UNIDIRECTIONAL);
        props.setSourceStaffIds(Arrays.asList(staffId));
        props.setTargetStaffIds(Arrays.asList(staffId));
        props.setRelationDescription(description);
        props.setIsEnabled(true);
        props.setPriority(0);
        
        meta.setResourceProps(props);
        meta.setName("独立完成关系");
        meta.setDescription(description);
        
        return meta;
    }

    /**
     * 创建交接关系
     *
     * @param sourceStaffId 源数字员工ID
     * @param targetStaffId 目标数字员工ID
     * @param dataTypes 交接数据类型
     * @param requireConfirmation 是否需要确认
     * @return 关系元数据
     */
    public static AIDigitalStaffRelationMeta createHandoverRelation(String sourceStaffId, String targetStaffId, 
                                                                   List<String> dataTypes, boolean requireConfirmation) {
        AIDigitalStaffRelationMeta meta = new AIDigitalStaffRelationMeta();
        AIDigitalStaffRelationMeta.Props props = new AIDigitalStaffRelationMeta.Props();
        
        props.setRelationType(DigitalStaffRelationType.HANDOVER);
        props.setDirection(DigitalStaffRelationDirection.UNIDIRECTIONAL);
        props.setSourceStaffIds(Arrays.asList(sourceStaffId));
        props.setTargetStaffIds(Arrays.asList(targetStaffId));
        props.setRelationDescription("工作交接关系");
        props.setIsEnabled(true);
        props.setPriority(1);
        
        // 配置交接参数
        AIDigitalStaffRelationMeta.RelationConfig config = new AIDigitalStaffRelationMeta.RelationConfig();
        AIDigitalStaffRelationMeta.HandoverConfig handoverConfig = new AIDigitalStaffRelationMeta.HandoverConfig();
        handoverConfig.setDataTypes(dataTypes);
        handoverConfig.setRequireConfirmation(requireConfirmation);
        handoverConfig.setTimeoutMinutes(30);
        handoverConfig.setRetryCount(3);
        handoverConfig.setTriggerCondition("TASK_COMPLETED");
        
        config.setHandoverConfig(handoverConfig);
        props.setRelationConfig(config);
        
        meta.setResourceProps(props);
        meta.setName("交接关系");
        meta.setDescription("从 " + sourceStaffId + " 到 " + targetStaffId + " 的工作交接");
        
        return meta;
    }

    /**
     * 创建汇报关系
     *
     * @param subordinateStaffId 下级数字员工ID
     * @param supervisorStaffId 上级数字员工ID
     * @param reportFrequency 汇报频率
     * @param realTimeReport 是否实时汇报
     * @return 关系元数据
     */
    public static AIDigitalStaffRelationMeta createReportRelation(String subordinateStaffId, String supervisorStaffId,
                                                                 String reportFrequency, boolean realTimeReport) {
        AIDigitalStaffRelationMeta meta = new AIDigitalStaffRelationMeta();
        AIDigitalStaffRelationMeta.Props props = new AIDigitalStaffRelationMeta.Props();
        
        props.setRelationType(DigitalStaffRelationType.REPORT);
        props.setDirection(DigitalStaffRelationDirection.UNIDIRECTIONAL);
        props.setSourceStaffIds(Arrays.asList(subordinateStaffId));
        props.setTargetStaffIds(Arrays.asList(supervisorStaffId));
        props.setRelationDescription("工作汇报关系");
        props.setIsEnabled(true);
        props.setPriority(2);
        
        // 配置汇报参数
        AIDigitalStaffRelationMeta.RelationConfig config = new AIDigitalStaffRelationMeta.RelationConfig();
        AIDigitalStaffRelationMeta.ReportConfig reportConfig = new AIDigitalStaffRelationMeta.ReportConfig();
        reportConfig.setReportFrequency(reportFrequency);
        reportConfig.setRealTimeReport(realTimeReport);
        reportConfig.setReportTemplate("标准工作汇报模板");
        reportConfig.setReportFormat("JSON");
        reportConfig.setDataScope(Arrays.asList("TASK_STATUS", "PROGRESS", "ISSUES"));
        
        config.setReportConfig(reportConfig);
        props.setRelationConfig(config);
        
        meta.setResourceProps(props);
        meta.setName("汇报关系");
        meta.setDescription("从 " + subordinateStaffId + " 到 " + supervisorStaffId + " 的工作汇报");
        
        return meta;
    }

    /**
     * 创建协商关系
     *
     * @param participantStaffIds 参与协商的数字员工ID列表
     * @param consultationTopic 协商主题
     * @param maxRounds 最大协商轮次
     * @return 关系元数据
     */
    public static AIDigitalStaffRelationMeta createConsultationRelation(List<String> participantStaffIds,
                                                                       String consultationTopic, int maxRounds) {
        AIDigitalStaffRelationMeta meta = new AIDigitalStaffRelationMeta();
        AIDigitalStaffRelationMeta.Props props = new AIDigitalStaffRelationMeta.Props();
        
        props.setRelationType(DigitalStaffRelationType.CONSULTATION);
        props.setDirection(DigitalStaffRelationDirection.BIDIRECTIONAL);
        props.setSourceStaffIds(participantStaffIds);
        props.setTargetStaffIds(participantStaffIds);
        props.setRelationDescription("协商讨论关系");
        props.setIsEnabled(true);
        props.setPriority(3);
        
        // 配置协商参数
        AIDigitalStaffRelationMeta.RelationConfig config = new AIDigitalStaffRelationMeta.RelationConfig();
        AIDigitalStaffRelationMeta.ConsultationConfig consultationConfig = new AIDigitalStaffRelationMeta.ConsultationConfig();
        consultationConfig.setConsultationTopic(consultationTopic);
        consultationConfig.setMaxRounds(maxRounds);
        consultationConfig.setTimeoutMinutes(60);
        consultationConfig.setDecisionAlgorithm("MAJORITY_VOTE");
        consultationConfig.setRequireHumanIntervention(false);
        consultationConfig.setConsultationRules(Arrays.asList("RESPECTFUL_COMMUNICATION", "EVIDENCE_BASED", "TIME_LIMITED"));
        
        config.setConsultationConfig(consultationConfig);
        props.setRelationConfig(config);
        
        meta.setResourceProps(props);
        meta.setName("协商关系");
        meta.setDescription("关于 " + consultationTopic + " 的协商关系");
        
        return meta;
    }

    /**
     * 创建汇集关系
     *
     * @param contributorStaffIds 贡献者数字员工ID列表
     * @param aggregatorStaffId 汇集者数字员工ID
     * @param aggregationStrategy 汇集策略
     * @return 关系元数据
     */
    public static AIDigitalStaffRelationMeta createAggregationRelation(List<String> contributorStaffIds,
                                                                      String aggregatorStaffId, String aggregationStrategy) {
        AIDigitalStaffRelationMeta meta = new AIDigitalStaffRelationMeta();
        AIDigitalStaffRelationMeta.Props props = new AIDigitalStaffRelationMeta.Props();
        
        props.setRelationType(DigitalStaffRelationType.AGGREGATION);
        props.setDirection(DigitalStaffRelationDirection.UNIDIRECTIONAL);
        props.setSourceStaffIds(contributorStaffIds);
        props.setTargetStaffIds(Arrays.asList(aggregatorStaffId));
        props.setRelationDescription("工作汇集关系");
        props.setIsEnabled(true);
        props.setPriority(4);
        
        // 配置汇集参数
        AIDigitalStaffRelationMeta.RelationConfig config = new AIDigitalStaffRelationMeta.RelationConfig();
        AIDigitalStaffRelationMeta.AggregationConfig aggregationConfig = new AIDigitalStaffRelationMeta.AggregationConfig();
        aggregationConfig.setAggregationStrategy(aggregationStrategy);
        aggregationConfig.setMaxWaitMinutes(120);
        aggregationConfig.setMinParticipants(contributorStaffIds.size());
        aggregationConfig.setTriggerCondition("ALL_CONTRIBUTORS_READY");
        aggregationConfig.setConflictResolutionStrategy("PRIORITY_BASED");
        aggregationConfig.setMergeRules(Arrays.asList("TIMESTAMP_ORDER", "PRIORITY_WEIGHT", "DATA_VALIDATION"));
        
        config.setAggregationConfig(aggregationConfig);
        props.setRelationConfig(config);
        
        meta.setResourceProps(props);
        meta.setName("汇集关系");
        meta.setDescription("多个员工向 " + aggregatorStaffId + " 汇集工作结果");
        
        return meta;
    }

    /**
     * 创建复合关系（交接/汇报）
     *
     * @param sourceStaffId 源数字员工ID
     * @param targetStaffId 目标数字员工ID
     * @param handoverDataTypes 交接数据类型
     * @param reportFrequency 汇报频率
     * @return 关系元数据
     */
    public static AIDigitalStaffRelationMeta createHandoverReportRelation(String sourceStaffId, String targetStaffId,
                                                                         List<String> handoverDataTypes, String reportFrequency) {
        AIDigitalStaffRelationMeta meta = new AIDigitalStaffRelationMeta();
        AIDigitalStaffRelationMeta.Props props = new AIDigitalStaffRelationMeta.Props();
        
        props.setRelationType(DigitalStaffRelationType.HANDOVER_REPORT);
        props.setDirection(DigitalStaffRelationDirection.UNIDIRECTIONAL);
        props.setSourceStaffIds(Arrays.asList(sourceStaffId));
        props.setTargetStaffIds(Arrays.asList(targetStaffId));
        props.setRelationDescription("交接汇报复合关系");
        props.setIsEnabled(true);
        props.setPriority(5);
        
        // 配置复合参数
        AIDigitalStaffRelationMeta.RelationConfig config = new AIDigitalStaffRelationMeta.RelationConfig();
        
        // 交接配置
        AIDigitalStaffRelationMeta.HandoverConfig handoverConfig = new AIDigitalStaffRelationMeta.HandoverConfig();
        handoverConfig.setDataTypes(handoverDataTypes);
        handoverConfig.setRequireConfirmation(true);
        handoverConfig.setTimeoutMinutes(30);
        handoverConfig.setRetryCount(3);
        handoverConfig.setTriggerCondition("TASK_COMPLETED");
        
        // 汇报配置
        AIDigitalStaffRelationMeta.ReportConfig reportConfig = new AIDigitalStaffRelationMeta.ReportConfig();
        reportConfig.setReportFrequency(reportFrequency);
        reportConfig.setRealTimeReport(false);
        reportConfig.setReportTemplate("交接汇报模板");
        reportConfig.setReportFormat("JSON");
        reportConfig.setDataScope(Arrays.asList("HANDOVER_STATUS", "TASK_PROGRESS", "QUALITY_METRICS"));
        
        config.setHandoverConfig(handoverConfig);
        config.setReportConfig(reportConfig);
        props.setRelationConfig(config);
        
        meta.setResourceProps(props);
        meta.setName("交接汇报关系");
        meta.setDescription("从 " + sourceStaffId + " 到 " + targetStaffId + " 的交接汇报关系");
        
        return meta;
    }
}
