package io.terminus.trantor2.service.dsl.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.service.dsl.properties.knowledgebase.AgentKnowledgeBase;
import io.terminus.trantor2.service.dsl.enums.ReasoningType;
import io.terminus.trantor2.service.dsl.properties.trigger.AgentTrigger;
import io.terminus.trantor2.service.dsl.properties.validation.Validator;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * AgentProperties
 *
 * <AUTHOR> Created on 2025/3/15 23:59
 */
@Setter
@Getter
@Schema(title = "Agent属性", description = "Agent属性")
public class AgentProperties extends ServiceProperties implements Validator, Serializable {
    private static final long serialVersionUID = -9171600114857814812L;

    /**
     * agent头像
     */
    private String avatar;

    /**
     * 关联场景
     */
    private RelatedScene relatedScene;

    /**
     * 模型类型
     */
    private ModelType modelType;

    /**
     * 大模型
     */
    private LlmModel model;

    /**
     * 动态模型配置（启用后优先生效）
     */
    private DynamicLlmModel dynamicModel;

    /**
     * 根据用户语言回复
     *
     * @deprecated 在generalProfessionalSkills中
     */
    @Deprecated
    private Boolean replyWithUserLanguage;

    /**
     * 欢迎词
     */
    private String greetings;

    /**
     * 欢迎词关联工具
     */
    private List<SkillTool> greetingsRelatedTools;

    /**
     * 开场白预置问题
     */
    private List<String> userQuestions;

    /**
     * 快捷问题
     */
    private List<QuickQuestion> quickQuestions;

    /**
     * 用户问题建议开关，如果开，则每次都返回3个用户建议问题
     */
    private boolean userQuestionsSuggest;

    /**
     * 是否用户自定义提示词
     */
    private boolean userQuestionsCustom;

    /**
     * 用户问题建议提示词
     */
    private String userQuestionsSuggestionPrompt;

    /**
     * Agent提示词
     */
    private String systemPrompt;

    /**
     * 技能列表
     */
    private List<Skill> skills;

    /**
     * 技能工具
     *
     * @deprecated 放入到Skill中
     */
    @Deprecated
    private List<SkillTool> skillTools;

    @Schema(description = "动态工具配置")
    private DynamicToolConfig dynamicToolConfig;

    /**
     * 触发器
     *
     * @deprecated 放入到Skill中
     */
    @Deprecated
    private List<AgentTrigger> triggers;

    /**
     * 入参结构
     */
    private List<Field> input;

    /**
     * 关联模型列表
     */
    private List<RelatedModel> relatedModels;

    /**
     * 推理框架
     */
    private String reasoningFramework;

    // /**
    // * 是否需要验证结果
    // */
    // private Boolean checkResult;

    /**
     * 权限项key
     */
    private String permissionKey;

    /**
     * 数据访问权限范围
     *
     * @deprecated 放入到skills中
     */
    @Deprecated
    private List<AgentDataScope> dataScopes;

    /**
     * 知识库
     *
     * @deprecated 放入到Skill中
     */
    @Deprecated
    private AgentKnowledgeBase knowledgeBase;

    /**
     * 术语映射预览结果(结构化的术语映射最后转化为文本，LLM 感知的内容是最终映射预览结果)
     *
     * @deprecated 也应该放在Skill中
     */
    @Deprecated
    private String termMappings;

    /**
     * 创建会话时的钩子
     */
    private AgentHook hookForSessionCreated;
    /**
     * 发送消息前的钩子
     */
    private AgentHook hookForBeforeSendMessage;
    /**
     * 发送消息后的钩子
     */
    private AgentHook hookForAfterSendMessage;

    /**
     * 长期记忆配置
     */
    private AgentLongTermMemoryConfig longTermMemoryConfig = new AgentLongTermMemoryConfig();

    private AgentDeepResearchConfig deepResearch = new AgentDeepResearchConfig();

    /**
     * 核心职责
     */
    private String coreResponsibilities;

    /**
     * 基础能力（勾选项）
     */
    private List<BasicAbility> basicAbilities;

    /**
     * 通用专业技能（勾选项）
     */
    private List<GeneralProfessionalSkill> generalProfessionalSkills;

    /**
     * 推理类型（当选择 DEEP_REASONING 时需要设置）
     */
    private ReasoningType reasoningType;

    /**
     * 基础能力
     */
    public enum BasicAbility {
        // 听（通过音频理解用户需求）
        LISTEN_AUDIO,

        // 说（通过语音回答用户问题）
        SPEAK_AUDIO,

        // 读（读取文字）
        READ_TEXT,

        // 读（读取图片）
        READ_IMAGE,

        // 读（读取视频）
        READ_VIDEO,

        // 读（读取常见办公文档）
        READ_DOCUMENT,

        // 写（生成文字）
        WRITE_TEXT,

        // 写（生成图片）
        WRITE_IMAGE,

        // 写（生成视频）
        WRITE_VIDEO,

        // 写（生成常见办公文档）
        WRITE_DOCUMENT,
    }

    /**
     * 通用专业技能
     */
    public enum GeneralProfessionalSkill {
        /** 管理认知 */
        MANAGEMENT_COGNITION,
        /** 数据分析能力 */
        DATA_ANALYSIS,
        /** 深度推理 */
        DEEP_REASONING,
        /** 国际化 */
        INTERNATIONALIZATION
    }

    public enum ModelType {
        /** 单模型 */
        SINGLE_MODEL,
        /** 动态模型 */
        DYNAMIC_MODEL
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        // 单模型或动态模型至少配置其一
        if (modelType == null) {
            errorContext.addErrorInfo("大模型未配置");

            if (modelType == ModelType.SINGLE_MODEL) {
                if (model == null) {
                    errorContext.addErrorInfo("模型未配置");
                } else {
                    if (model.getModelPublisher() == null) {
                        errorContext.addErrorInfo("大模型开发商未配置");
                    }
                    if (model.getName() == null) {
                        errorContext.addErrorInfo("大模型未配置");
                    }
                }
            }

            if (modelType == ModelType.DYNAMIC_MODEL) {
                if (dynamicModel != null) {
                    if (dynamicModel.getRoutes() == null || dynamicModel.getRoutes().isEmpty()) {
                        errorContext.addErrorInfo("动态模型路由未配置");
                    }
                } else {
                    errorContext.addErrorInfo("动态模型未配置");
                }
            }
        }

        if (systemPrompt == null) {
            errorContext.addErrorInfo("提示词未配置");
        }

        if (input != null) {
            errorContext.addTag("变量");
            input.forEach(i -> i.validate(errorContext));
            errorContext.removeTag();
        }

        if (userQuestionsSuggest) {
            if (userQuestionsSuggestionPrompt != null) {
                errorContext.addErrorInfo("用户问题建议提示词未配置");
            }
        }

        longTermMemoryConfig.getMemoryVariables().forEach(i -> {
            if (i.getName() != null && i.getName().length() > 64) {
                errorContext.addErrorInfo("长期记忆变量名称长度不能超过64：" + i.getName());
            }
            if (i.getDescription() != null && i.getDescription().length() > 128) {
                errorContext.addErrorInfo("长期记忆变量描述长度不能超过128：" + i.getDescription());
            }
        });
    }
}
