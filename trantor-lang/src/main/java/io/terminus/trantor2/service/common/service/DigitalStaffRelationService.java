package io.terminus.trantor2.service.common.service;

import io.terminus.trantor2.service.common.meta.AIDigitalStaffRelationMeta;
import io.terminus.trantor2.service.common.enums.DigitalStaffRelationType;

import java.util.List;
import java.util.Map;

/**
 * 数字员工关系管理服务接口
 *
 * <AUTHOR> Created on 2025/9/4
 */
public interface DigitalStaffRelationService {

    /**
     * 创建数字员工关系
     *
     * @param relationMeta 关系元数据
     * @return 关系ID
     */
    String createRelation(AIDigitalStaffRelationMeta relationMeta);

    /**
     * 更新数字员工关系
     *
     * @param relationId 关系ID
     * @param relationMeta 关系元数据
     * @return 是否更新成功
     */
    boolean updateRelation(String relationId, AIDigitalStaffRelationMeta relationMeta);

    /**
     * 删除数字员工关系
     *
     * @param relationId 关系ID
     * @return 是否删除成功
     */
    boolean deleteRelation(String relationId);

    /**
     * 根据ID查询关系
     *
     * @param relationId 关系ID
     * @return 关系元数据
     */
    AIDigitalStaffRelationMeta getRelationById(String relationId);

    /**
     * 查询数字员工的所有关系
     *
     * @param staffId 数字员工ID
     * @return 关系列表
     */
    List<AIDigitalStaffRelationMeta> getRelationsByStaffId(String staffId);

    /**
     * 根据关系类型查询关系
     *
     * @param relationType 关系类型
     * @return 关系列表
     */
    List<AIDigitalStaffRelationMeta> getRelationsByType(DigitalStaffRelationType relationType);

    /**
     * 查询两个数字员工之间的关系
     *
     * @param sourceStaffId 源数字员工ID
     * @param targetStaffId 目标数字员工ID
     * @return 关系列表
     */
    List<AIDigitalStaffRelationMeta> getRelationsBetweenStaff(String sourceStaffId, String targetStaffId);

    /**
     * 启用关系
     *
     * @param relationId 关系ID
     * @return 是否启用成功
     */
    boolean enableRelation(String relationId);

    /**
     * 禁用关系
     *
     * @param relationId 关系ID
     * @return 是否禁用成功
     */
    boolean disableRelation(String relationId);

    /**
     * 批量创建关系
     *
     * @param relationMetas 关系元数据列表
     * @return 创建结果，key为关系元数据，value为关系ID
     */
    Map<AIDigitalStaffRelationMeta, String> batchCreateRelations(List<AIDigitalStaffRelationMeta> relationMetas);

    /**
     * 验证关系配置是否有效
     *
     * @param relationMeta 关系元数据
     * @return 验证结果
     */
    ValidationResult validateRelation(AIDigitalStaffRelationMeta relationMeta);

    /**
     * 获取数字员工的关系图
     *
     * @param staffId 数字员工ID
     * @param depth 关系深度
     * @return 关系图数据
     */
    RelationGraph getRelationGraph(String staffId, int depth);

    /**
     * 查找关系路径
     *
     * @param sourceStaffId 源数字员工ID
     * @param targetStaffId 目标数字员工ID
     * @return 关系路径列表
     */
    List<RelationPath> findRelationPaths(String sourceStaffId, String targetStaffId);

    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private List<String> errors;
        private List<String> warnings;

        // getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
        public List<String> getWarnings() { return warnings; }
        public void setWarnings(List<String> warnings) { this.warnings = warnings; }
    }

    /**
     * 关系图
     */
    class RelationGraph {
        private String centerStaffId;
        private List<RelationNode> nodes;
        private List<RelationEdge> edges;

        // getters and setters
        public String getCenterStaffId() { return centerStaffId; }
        public void setCenterStaffId(String centerStaffId) { this.centerStaffId = centerStaffId; }
        public List<RelationNode> getNodes() { return nodes; }
        public void setNodes(List<RelationNode> nodes) { this.nodes = nodes; }
        public List<RelationEdge> getEdges() { return edges; }
        public void setEdges(List<RelationEdge> edges) { this.edges = edges; }
    }

    /**
     * 关系节点
     */
    class RelationNode {
        private String staffId;
        private String staffName;
        private String staffType;
        private int level;

        // getters and setters
        public String getStaffId() { return staffId; }
        public void setStaffId(String staffId) { this.staffId = staffId; }
        public String getStaffName() { return staffName; }
        public void setStaffName(String staffName) { this.staffName = staffName; }
        public String getStaffType() { return staffType; }
        public void setStaffType(String staffType) { this.staffType = staffType; }
        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }
    }

    /**
     * 关系边
     */
    class RelationEdge {
        private String relationId;
        private String sourceStaffId;
        private String targetStaffId;
        private DigitalStaffRelationType relationType;
        private boolean enabled;

        // getters and setters
        public String getRelationId() { return relationId; }
        public void setRelationId(String relationId) { this.relationId = relationId; }
        public String getSourceStaffId() { return sourceStaffId; }
        public void setSourceStaffId(String sourceStaffId) { this.sourceStaffId = sourceStaffId; }
        public String getTargetStaffId() { return targetStaffId; }
        public void setTargetStaffId(String targetStaffId) { this.targetStaffId = targetStaffId; }
        public DigitalStaffRelationType getRelationType() { return relationType; }
        public void setRelationType(DigitalStaffRelationType relationType) { this.relationType = relationType; }
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
    }

    /**
     * 关系路径
     */
    class RelationPath {
        private List<String> staffIds;
        private List<String> relationIds;
        private int pathLength;
        private double pathWeight;

        // getters and setters
        public List<String> getStaffIds() { return staffIds; }
        public void setStaffIds(List<String> staffIds) { this.staffIds = staffIds; }
        public List<String> getRelationIds() { return relationIds; }
        public void setRelationIds(List<String> relationIds) { this.relationIds = relationIds; }
        public int getPathLength() { return pathLength; }
        public void setPathLength(int pathLength) { this.pathLength = pathLength; }
        public double getPathWeight() { return pathWeight; }
        public void setPathWeight(double pathWeight) { this.pathWeight = pathWeight; }
    }
}
