package io.terminus.trantor2.service.dsl.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Data
@Getter
@Setter
@Schema(title = "深入研究", description = "深入研究配置")
public class AgentDeepResearchConfig {
    @Schema(title = "深入研究开关", description = "深入研究开关")
    private Boolean enabled = false;
    @Schema(title = "最大步骤数", description = "最大步骤数")
    private Integer maxStepNum = 5;
    @Schema(title = "最大搜索结果数", description = "最大搜索结果数")
    private Integer maxSearchResults = 12;
    @Schema(title = "自动接受计划", description = "自动接受计划", defaultValue = "false")
    private Boolean autoAcceptedPlan = false;
}
