package io.terminus.trantor2.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数字员工关系类型枚举
 *
 * <AUTHOR> Created on 2025/9/4
 */
@AllArgsConstructor
@Getter
public enum DigitalStaffRelationType {

    /**
     * 独立完成 - 数字员工独立完成任务，无需与其他员工协作
     */
    INDEPENDENT("独立完成"),

    /**
     * 交接 - 一个数字员工完成任务后将结果交接给另一个数字员工
     */
    HANDOVER("交接"),

    /**
     * 汇报 - 下级数字员工向上级数字员工汇报工作进展或结果
     */
    REPORT("汇报"),

    /**
     * 协商 - 多个数字员工之间进行协商讨论以达成共识
     */
    CONSULTATION("协商"),

    /**
     * 交接/汇报 - 既有交接又有汇报的复合关系
     */
    HANDOVER_REPORT("交接/汇报"),

    /**
     * 汇集 - 多个数字员工的工作结果汇集到一个数字员工处进行整合
     */
    AGGREGATION("汇集");

    private final String display;
}
