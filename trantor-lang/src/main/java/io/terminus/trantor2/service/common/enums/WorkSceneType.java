package io.terminus.trantor2.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工作场景类型枚举
 *
 * <AUTHOR> Created on 2025/9/4
 */
@AllArgsConstructor
@Getter
public enum WorkSceneType {

    /**
     * 简单场景 - 单个数字员工独立完成的场景
     */
    SIMPLE("简单场景"),

    /**
     * 流水线场景 - 多个数字员工按顺序协作的场景
     */
    PIPELINE("流水线场景"),

    /**
     * 协作场景 - 多个数字员工并行协作的场景
     */
    COLLABORATIVE("协作场景"),

    /**
     * 层级场景 - 具有上下级关系的数字员工协作场景
     */
    HIERARCHICAL("层级场景"),

    /**
     * 混合场景 - 包含多种关系类型的复杂场景
     */
    HYBRID("混合场景"),

    /**
     * 自定义场景 - 用户自定义的特殊场景
     */
    CUSTOM("自定义场景");

    private final String display;
}
