server:
  port: 8080
  tomcat:
    basedir: logs
    accesslog:
      enabled: ${ACCESSLOG_ENABLED:false}
      pattern: '%{yyyy-MM-dd HH:mm:ss}t|[%I][%{reqId}i]-|cost=%D remoteIp=%a localIp=%A url=%U status=%s'
spring:
  application:
    name: trantor2
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  servlet:
    multipart:
      enabled: ${upload_file_standard:true}
      max-file-size: ${upload_max_file:500MB}
      max-request-size: ${upload_max_request:500MB}
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${TRANTOR_MYSQL_HOST:${MYSQL_HOST:127.0.0.1}}:${TRANTOR_MYSQL_PORT:${MYSQL_PORT:3306}}/${TRANTOR_MYSQL_DATABASE:${MYSQL_DATABASE:trantor2}}?useUnicode=true&characterEncoding=utf-8&useSSL=false
    username: ${TRANTOR_MYSQL_USERNAME:${MYSQL_USERNAME:root}}
    password: ${TRANTOR_MYSQL_PASSWORD:${MYSQL_PASSWORD:password}}
    hikari:
      minimumIdle: 3 # 小于等于 maximumPoolSize，小于 maximumPoolSize 时，idleTimeout 会生效
      maximumPoolSize: 10 # default 10, 不建议调超过 20 个
      maxLifetime: 3600000 # 1 hour
      idleTimeout: 300000 # 5 minutes, 必须小于 maxLifetime 否则该配置会被重置为 0, 不起作用
      transaction-isolation: TRANSACTION_READ_COMMITTED # 默认读已提交
  jpa:
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        globally_quoted_identifiers: false
        query:
          plan_cache_max_size: 64
          plan_parameter_metadata_max_size: 32
          in_clause_parameter_padding: true
          jdbc:
            batch_size: 500
    #                order_updates: true
    hibernate:
      # erda 容器 HOME 是 /root，线上根据环境变量 AUTO_DDL_TYPE（默认为 none）
      # 线下基本上 HOME 不会 /root 当 gravity.enabled = true 设置为 none, 否则设置为本地环境变量 AUTO_DDL_TYPE（默认为 none）
      ddl-auto: ${AutoDDLTypeMap.${HOME:/root}:${AutoDDLTypeMap.${gravity.enabled:false}:${AUTO_DDL_TYPE:none}}}
    database-platform: org.hibernate.dialect.MySQLDialect
    defer-datasource-initialization: false
  sql:
    init:
      # erda 容器 HOME 是 /root，线上根据环境变量 SQL_INIT_MODE（默认为 ALWAYS）
      # 线下基本上 HOME 不会 /root 当 gravity.enabled = true 设置为 NEVER, 否则设置为本地环境变量 SQL_INIT_MODE（默认为 ALWAYS）
      mode: ${InitModeMap.${HOME:/root}:${InitModeMap.${gravity.enabled:false}:${SQL_INIT_MODE:ALWAYS}}}
  data:
    redis:
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      sentinel:
        master: ${MASTER_NAME:} # redis哨兵模式下master名，redis哨兵模式下必须配置
        nodes: ${REDIS_SENTINELS:} # redis哨兵模式下sentinel节点，格式为IP:PORT,多个节点间使用逗号分割
      cluster:
        nodes: ${REDIS_CLUSTER_NODES:} # redis集群模式下节点，格式为IP:PORT,多个节点间使用逗号分割
        max-redirects: ${REDIS_CLUSTER_MAX_REDIRECTS:3} # redis集群模式下最大重定向次数
      lettuce:
        pool:
          max-active: ${REDIS_POOL_MAX_ACTIVE:8}
          max-wait: ${REDIS_POOL_MAX_WAIT:-1}
          max-idle: ${REDIS_POOL_MAX_IDLE:8}
          min-idle: ${REDIS_POOL_MIN_IDLE:0}
      timeout: ${REDIS_TIMEOUT:500}
  flyway:
    enabled: false
  cloud:
    nacos:
      discovery:
        enabled: ${NACOS_DISCOVERY_ENABLED:true}
        server-addr: ${NACOS_ADDRESS:}
        namespace: ${NACOS_TENANT_ID:}
        isUseCloudNamespaceParsing: false # 否则 2.x 开始会读环境变量 TENANT_ID，但 erda 注入的 TENANT_ID 和 NACOS_TENANT_ID 并不一致
  ai:
    openai:
      api-key: ${ERDA_AI_PROXY_API_KEY:your-openai-api-key}
      base-url: ${ERDA_AI_PROXY_BASE_URL:https://ai-proxy.erda.cloud/v1}
      chat:
        options:
          model: ${ERDA_AI_PROXY_MODEL:gpt-4.1}
          temperature: ${ERDA_AI_PROXY_TEMPERATURE:0.7}
    mcp:
      server:
        enabled: true
        sse-endpoint: /api/trantor/mcp/built-in/sse
        sse-message-endpoint: /api/trantor/mcp/built-in/mcp/message

feign:
  client:
    config:
      default:
        connectTimeout: 30000 # 默认 30s, 可用环境变量 FEIGN_CLIENT_CONFIG_DEFAULT_CONNECT_TIMEOUT 覆盖
        readTimeout: 55000 # 默认 55s, 可用环境变量 FEIGN_CLIENT_CONFIG_DEFAULT_READ_TIMEOUT 覆盖
logging:
  level:
    root: ${TRANTOR_LOG_LEVEL:warn}
    io.terminus.trantor2: ${TRANTOR_LOG_LEVEL:warn}
    io.terminus.trantor2.meta: ${TRANTOR_META_LOG_LEVEL:info}
    com.zaxxer.hikari: ${HIKARI_LOG_LEVEL:info}
    io.terminus.trantor2.model.runtime.meta.transaction: ${TRANTOR_TRANSACTION_LOG_LEVEL:info}
    io.terminus.iam.sdk: ${IAM_SDK_LOG_LEVEL:info}

management:
  health:
    defaults:
      enabled: false
    # 按需打开 db 等中间件健康检查
  endpoints:
    web:
      exposure:
        include: "health,platform,migration"

springdoc:
  api-docs:
    enabled: ${SWAGGER_ENABLE:false}
  packages-to-scan: io.terminus.trantor2
  swagger-ui:
    filter: true

cloud:
  storage:
    endpoint: ${OSS_ENDPOINT:${OSS_HOST:oss-cn-hangzhou.aliyuncs.com}}
    ossRegion: ${OSS_REGION:oss-cn-hangzhou}
    privateBucket: ${OSS_BUCKET:${SERVER_BUCKET:terminus-new-trantor}}
    publicBucket: ${PUBLIC_BUCKET:terminus-new-trantor}
    accessKey: ${OSS_ACCESS_KEY_ID:${OSS_AK:}}
    accessSecret: ${OSS_ACCESS_KEY_SECRET:${OSS_AS:}}
    connectTimeout: ${OSS_TIMEOUT:100}
    fileStoreType: ${FILE_STORE_TYPE:oss}
    privateRead: ${PRIVATE_READ:false}
    urlExpireTime: ${URL_EXPIRE_TIME:3600000}
    cdnEndpoint: ${OSS_CDN_ENDPOINT:}

terminus:
  mqServerAddress: ${MQ_SERVER_ADDRESS:127.0.0.1:9876}
  topic: ${MQ_TOPIC:TRANTOR2_TOPIC}
  producerGroup: ${PRODUCER_GROUP:TRANTOR2Producer}
  consumerGroup: ${CONSUMER_GROUP:TRANTOR2ConsoleConsumer}
  clientType: ${MQ_CLIENT_TYPE:ROCKETMQ}
  aliyun:
    accessKey: ${ROCKET_MQ_ALIYUN_ACCESS_KEY:}
    secretKey: ${ROCKET_MQ_ALIYUN_SECRET_KEY:}
  redis:
    cachable: true
  config:
    discovery: ${TERMINUS_CONFIG_DISCOVERY_ENABLED:${NACOS_DISCOVERY_ENABLED:true}}
    enabled: ${TERMINUS_CONFIG_ENABLED:${NACOS_CONFIG_ENABLED:false}}


trantor2:
  version: @project.version@
  mq:
    consumer-group: TRANTOR2RuntimeConsumer
    topic: ${TRANTOR2_MQ_TOPIC:TRANTOR2_TOPIC}
  model:
    data:
      transaction:
        timeout: ${TRANSACTION_TIMEOUT:6000000}
    management:
      modelRegex: ${MODEL_RULE_REGEX:}
      search-dump-url: ${SEARCH_DUMP_URL:http://trantor-dump:8082}
      max-retries: ${TRANTOR_MODEL_MAX_RETRIES:5} # 配置最大重试次数，用于数据源为主从 proxy，从库 information_schema 数据库元数据生成存在时延的情况
      retry-interval: ${TRANTOR_MODEL_RETRY_INTERVAL:100} # 配置最大重试间隔，用于数据源为主从 proxy，从库 information_schema 数据库元数据生成存在时延的情况
  service:
    engine-name: ${SERVICE_ENGINE_NAME:service-engine}
    loader-cache-enabled: true
    origin-org-enabled: ${SERVICE_ORIGIN_ORG_ENABLED:false}
    origin-org-module-key: ${SERVICE_ORIGIN_ORG_MODULE_KEY:GEN_MD}
    auditing-log-enabled: ${SERVICE_AUDITING_LOG_ENABLED:false}
    new-status-enabled: ${SERVICE_NEW_STATUS_ENABLED:true}
    programmable-config:
      http-node-default-headers:
        - Trantor2-Team
        - Trantor2-Portal-Key
        - Trantor2-Team-Code
        - Cookie
        - Referer
        - Trantor2-Current-User
        - Trantor2-ORIGIN-ORG-ID
        - Trantor2-Tenant-Id
      ext-system-variable:
        http-headers: ${SERVICE_EXT_SYS_VAR_HEADER_KEYS:Cookie,Referer,Trantor2-Current-User}
    ai-summary-auto: ${SERVICE_AI_SUMMARY_AUTO:false}
    check-sql-table-cross-module: true
    async-pool:
      core-size: 2
      max-size: 20
      queue-capacity: 1000
      thread-name-prefix: service-async-
      keep-alive: 60
    close-event-create-modules: # 关闭事件创建的模块，这些模块的事件不允许被创建
      - terp@test_TB2B
      - terp@GEN
    allowed-upload-files: jpg,jpeg,png,gif,doc,docx,xls,xlsx,ppt,pptx,pdf,txt,zip,rar
  console:
    enabled: true # 启用 console 相关功能（主要是加载 bean）
    domain: ${DICE_PROJECT_NAME}-console-${DICE_WORKSPACE}.${DICE_ROOT_DOMAIN}
    iam-domain: ${CONSOLE_IAM_DOMAIN:}
    public-market:
      env: staging
      project: T-Project
      organization: terminus
  portal:
    main-domain: ${PORTAL_MAIN_DOMAIN:}
    app-domain: ${APP_PORTAL_MAIN_DOMAIN:}
    iam-domain: ${PORTAL_IAM_DOMAIN:}
  api-docs:
    title: ${DOCS_TITLE:Trantor2 API}
    version: ${DOCS_VERSION:1.0.0}
    depth: ${DOC_ARGS_DEPTH:4}
    open-api-version: v31
  runtime:
    default-mode: ${RUNTIME_DEPLOY_MODE:true}
    preload-meta: ${START_PRELOAD_META:false}
    modules: ## 模块列表
      whitelist: ## 模块加载白名单
      blocklist: ## 模块加载黑名单，例如sd模块是独立runtime，这里就不加载sd模块了
  task:
    migration:
      simple-job:
        enabled: true
    auto-snapshot:
      enabled: true
    auto-gc:
      enabled: true
  meta:
    enable: ${META_SYNC_ENABLE:true}
    host: ${META_SYNC_HOST:https://console-staging.app.terminus.io}
    token: ${META_SYNC_TOKEN:trantor2}
    event:
      type: ${TRANTOR_META_EVENT_TYPE:redis} # mq or redis
      retry:
        retries: ${TRANTOR2_META_EVENT_RETRY_RETRIES:1} # 默认重试次数
        delay: ${TRANTOR2_META_EVENT_RETRY_DELAY:1s} # 默认重试延迟时间
      merge:
        enabled: ${TRANTOR2_META_EVENT_MERGE_ENABLED:false} # 默认关闭，erda.yml 在开发测试注入 true
        permits-per-second: ${TRANTOR2_META_EVENT_MERGE_PERMITS_PER_SECOND:3} # 每秒允许的最请求数
        buffer-threshold: ${TRANTOR2_META_EVENT_MERGE_BUFFER_THRESHOLD:10} # 缓冲区数量阈值
        buffer-timeout: ${TRANTOR2_META_EVENT_MERGE_BUFFER_TIMEOUT:10s} # 缓冲区超时时间
  permission:
    job:
      # 权限快照任务
      snapshot:
        # 是否开启
        enabled: true
        # 定时策略，每天凌晨3点执行
        cron: 0 0 3 * * ?
        # teamCode白名单，多个以逗号分割，默认 <<ALL>> 表示所有teamCode
        teamCodes: <<ALL>>
      # 权限授权视图
      permission-authorization-view:
        # 是否开启
        enabled: true
        # 定时策略，每天凌晨4点执行
        cron: 0 0 4 * * ?
        # teamCode白名单，多个以逗号分割，默认 <<ALL>> 表示所有teamCode
        teamCodes: <<ALL>>
  lock:
    default-timeout: PT4H
  nexus:
    pub:
      host: ${TRANTOR_PUBLIC_NEXUS_HOST:https://repo.terminus.io:443}
      username: ${TRANTOR_PUBLIC_NEXUS_USERNAME:trantor2-artifact}
      password: ${TRANTOR_PUBLIC_NEXUS_PASSWORD:3HWG4kqVZEnFknJ}
      repository: ${TRANTOR_PUBLIC_NEXUS_REPOSITORY:trantor2-artifact}
    local:
      host: ${TRANTOR_LOCAL_NEXUS_HOST:http://127.0.0.1:8081}
      username: ${TRANTOR_LOCAL_NEXUS_USERNAME:}
      password: ${TRANTOR_LOCAL_NEXUS_PASSWORD:}
      repository: ${TRANTOR_LOCAL_NEXUS_REPOSITORY:}
      sync-enabled: ${TRANTOR_LOCAL_NEXUS_SYNC_ENABLED:false}
      dev-enabled: ${TRANTOR_LOCAL_NEXUS_DEV_ENABLED:false}
      enabled: ${TRANTOR_LOCAL_NEXUS_SYNC_ENABLED:${TRANTOR_LOCAL_NEXUS_DEV_ENABLED:false}}
  datasource:
    sharding-sphere-sql-show: true #当使用sharding数据源时，是否需要打印物理sql，默认true打印
    data-source-password-decrypt-key: terminus_trantor #数据源密码加密密钥key，该配置的值来自于前后端约定，如果修改该值，需要前后端一起修改
    data-source-password-decrypt-iv: terminus_trantor #数据源密码加密密钥iv，该配置的值来自于前后端约定，如果修改该值，需要前后端一起修改
  dors:
    host: ${DORS_HOST:http://dors-web:3000}
  trigger:
    group-prefix: Trigger_
    topic: ${terminus.topic}
    pool-threads: 0
    logger-enable: true
  license:
    mock-enable: true
  web:
    filters:
      - filter: io.terminus.trantor2.common.filter.TrantorContextInitFilter
        order: 30
        urlPatterns:
          - /*
      - filter: io.terminus.trantor2.common.filter.TrantorFilter
        order: 31
        urlPatterns:
          - /*
      - filter: io.terminus.trantor2.common.filter.TrantorUserFilter
        order: 32
        urlPatterns:
          - /*
      - filter: io.terminus.trantor2.common.filter.TrantorCommonHeaderFilter
        order: 33
        urlPatterns:
          - /api/*
          - /sse
          - /mcp/message
      - filter: io.terminus.trantor2.console.filter.TrantorContextFilter
        order: 34
        urlPatterns:
          - /*
      - filter: io.terminus.trantor2.console.filter.FuncSwitchFilter
        order: -11
        urlPatterns:
          - /*
      - filter: io.terminus.trantor2.console.filter.TeamAccessFilter
        order: -10
        urlPatterns:
          - /*
  otel:
    enabled: false
influxdb:
  url: ${INFLUXDB_URL:http://127.0.0.1:8086}
  token: ${INFLUXDB_TOKEN:}
  org: ${INFLUXDB_ORG:terminus}
  bucket: ${INFLUXDB_BUCKET:trantor}
milvus:
  host: ************
  port: 19530
  username: root
  password: Milvus
ai:
  enable: ${AI_ENABLE:true}
  agent:
    loader-cache-enabled: true
  t-ai-domain: ${AI_T_AI_DOMAIN:https://t-ai-aigc.app.terminus.io}
  ai-proxy:
    domain: ${AI_PROXY_DOMAIN:https://ai-proxy.erda.cloud}
    access-key-id: ${AI_PROXY_ACCESS_KEY:f85e417827fc473ea3a4c1156c288649}
  platform-domain: ${AI_PLATFORM_DOMAIN:https://t-ai-platform-runtime.app.terminus.io}
  model-filter:
    name-content: ${AI_MODEL_FILTER_NAME_CONTENT:embedding}
    name-exclude: ${AI_MODEL_FILTER_NAME_EXCLUDE:gpt-4o-mini}
  project-id: ${AI_PROJECT_ID:1}
  project-code: ${AI_PROJECT_CODE:t_ai}
  branch-id: ${AI_BRANCH_ID:}
  portal-key: ${AI_PORTAL_KEY:t_ai_platform}
  access-key: ${AI_APP_KEY:}
  access-secret: ${AI_APP_SECRET:}
  storage-path: ${AI_STORAGE_PATH:/data/ai/storage}
  async-context-clean: ${AI_ASYNC_CONTEXT_CLEAN:false}
  sse-message-body-structured: ${AI_SSE_MESSAGE_BODY_STRUCTURED:false}
  i18n-translation:
    translation-batch-size: ${AI_I18N_TRANSLATION_BATCH_SIZE:50}
    translation-service-key: ${AI_I18N_TRANSLATION_SERVICE_KEY:t_ai_management$i18n_text_translate}
    translation-proofread-service-key: ${AI_I18N_TRANSLATION_PROOFREAD_SERVICE_KEY:t_ai_management$i18n_text_translate_proofread}
    translation-proofread-channel: ${AI_I18N_TRANSLATION_PROOFREAD_CHANNEL:API}
    translation-proofread-rate: ${AI_I18N_TRANSLATION_PROOFREAD_RATE:0.6}
  proxy:
    enable: ${AI_PROXY_ENABLE:true}
    module-keys: ai_app_build,t_ai_management,knowledge_application
    url-mappings:
      - /api/trantor/meta/query/tree-node/single/t_ai*
      - /api/trantor/agent/execute/t_ai*
    preserver-cookies: ${AI_PROXY_PRESERVER_COOKIES:false}
    preserver-referer: ${AI_PROXY_PRESERVER_REFERER:false}
    ignore-headers:
      - Content-Length
  memory:
    chat-store: db
    chat-cache-ttl: 1d
    chat-history-model: sys_common$chat_history_record
    chat-history-message-model: sys_common$chat_history_message_record
    chat-summary:
      type: round
      model-provider: openai
      model-name: gpt-4.1
  reasoningFrameworks:
    - name: ReAct
      key: ReAct
    - name: 思维链
      key: CoT
console-ai:
  relayAIServerDomain: ${RELAY_AI_SERVER_DOMAIN:http://t-ai2}

iam:
  mock: ${IAM_MOCK:false}
  host: ${IAM_HOST:127.0.0.1}
  access-key: ${IAM_ACCESS_KEY:console}
  access-secret: ${IAM_ACCESS_SECRET:d88d437db773499991683df4bf09876b}
  internal:
    application-key: ${IAM_APPLICATION_KEY:console}
  web:
    order: 35 # 实际值：Ordered.HIGHEST_PRECEDENCE + order
    request-white-list:
      - /api/trantor/mcp/built-in/**
      - /v3/api-docs/**
      - /swagger-ui/**
      - /swagger-ui.html
      - /swagger-resources/**
      - /doc.html
      - /webjars/**
      - /actuator/**
      - /favicon.ico
      - /api/trantor/workflow/employee/**
      - /api/trantor/workflow/notice/**
      - /api/trantor/workflow/group/**
      - /api/trantor/workflow/user/**
      - /api/trantor/workflow/v2/instances/**
      - /api/trantor/workflow/v2/definitions/**
      - /api/trantor/console/user/logout
      - /api/trantor/console/user/login
      - /api/trantor/portal/user/logout
      - /api/trantor/portal/user/login
      - /api/trantor/doc/engine/action/**
      - /api/trantor/rule/engine/**
      - /api/trantor/console/rule/engine/**
      - /api/trantor/portal/current
      - /api/trantor/code-rule/**
      - /api/trantor/portal/ops/permission-cache/**
      - /api/internal/**
      - /api/trantor/meta/ops/**
      - /api/trantor/devops/**
      - /api/trantor/console/doc/engine/eventSync/**
      - /api/trantor/console/remote/**
      - /api/trantor/service/management/http-report
      - /api/trantor/meta/tag/last
      - /api/trantor/meta/tag/zip
      - /api/trantor/service/internal/**
      - /api/trantor/datasource/debug/**
      - /api/trantor/console/rt-ctl/**
      - /api/trantor/platform
    accessControlWhiteList:
      - /api/console/notice/**
      - /api/console/print/**
      - /api/trantor/ai/**
      - /api/trantor/service/engine/execute/ai*
      - /api/trantor/service/engine/execute/t_ai*
      - /api/trantor/service/engine/execute/ai-chat/ai*
      - /api/trantor/service/engine/execute/ai-chat/t_ai*
      - /api/trantor/agent/execute/ai*
      - /api/trantor/agent/execute/t_ai*
      - /api/trantor/meta/query/tree-node/single/t_ai*
      - /api/trantor/console/permission/package/create
      - /api/trantor/console/permission/package/paging
trantor:
  engine:
    host: http://trantor2-starter-15e9c151f2.project-190-dev.svc.cluster.local:8080

InitModeMap:
  '/root': ${SQL_INIT_MODE:ALWAYS}
  'true': NEVER

AutoDDLTypeMap:
  '/root': ${AUTO_DDL_TYPE:none}
  'true': none

terminus-process:
  client:
    url: ${PROCESS_URL:http://localhost:8080}
