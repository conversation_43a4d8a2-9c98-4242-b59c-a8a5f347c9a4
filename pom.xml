<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>trantor-engines</module>
        <module>trantor-console</module>
        <module>trantor-common</module>
        <module>trantor-runtime</module>
        <module>trantor-unify-runtime</module>
        <module>trantor-test-tools</module>
        <module>trantor-meta</module>
        <module>trantor-lang</module>
        <module>trantor-ide</module>
        <module>trantor-iam-adapter</module>
        <module>trantor-maven-plugin</module>
        <module>trantor-dors</module>
    </modules>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.12</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>io.terminus.trantor2</groupId>
    <artifactId>trantor-parent</artifactId>
    <version>${revision}</version>

    <name>Trantor</name>
    <description>Connector on Pro-Code and No-Code</description>
    <properties>
        <!-- master 分支始终使用3.0.DEV-SNAPSHOT，develop分支始终使用3.0.TEST-SNAPSHOT，release时注意在release分支修改 -->
        <revision>3.0.TEST-SNAPSHOT</revision>
        <!-- 与iam的main分支pom version保持一致 -->
        <iam.version>0.13.0-SNAPSHOT</iam.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>17</java.version>
        <nexus.release.url>https://repo.terminus.io/repository/releases/</nexus.release.url>
        <nexus.snapshot.url>https://repo.terminus.io/repository/snapshots/</nexus.snapshot.url>
        <spring-boot.version>3.2.12</spring-boot.version>
        <spring-cloud.version>2023.0.1</spring-cloud.version>
        <spring-web.version>6.1.19</spring-web.version>
        <spring-cloud-alibaba.version>2023.0.1.0</spring-cloud-alibaba.version>
        <spring-security.version>6.2.3</spring-security.version>
        <spring-ai-bom.version>1.0.0</spring-ai-bom.version>
        <httpclient.version>4.5.14</httpclient.version>
        <httpmime.version>4.5.14</httpmime.version>
        <groovy-jsr223.version>4.0.24</groovy-jsr223.version>
        <javapoet.version>1.13.0</javapoet.version>
        <checkstyle.plugin.version>3.1.2</checkstyle.plugin.version>
        <mysql-driver.version>8.0.33</mysql-driver.version>
        <auto-service.version>1.0.1</auto-service.version>
        <caffeine.version>2.9.3</caffeine.version>
        <jsqlparser.version>4.5</jsqlparser.version>
        <sql-formatter.version>2.0.5</sql-formatter.version>
        <springdoc.version>2.6.0</springdoc.version>
        <swagger3.version>2.2.22</swagger3.version>
        <hutool.version>5.8.27</hutool.version>
        <lombok.version>1.18.28</lombok.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <trantor.workflow.version>3.0.25.06.1.RELEASE</trantor.workflow.version>
        <terminus-rocketmq>3.0.1.RELEASE</terminus-rocketmq>
        <janino.version>3.1.7</janino.version>
        <sharding-jdbc.version>5.5.0</sharding-jdbc.version>
        <commons-lang.version>3.12.0</commons-lang.version>
        <commons-codec.version>1.15</commons-codec.version>
        <hibernate.version>6.4.4.Final</hibernate.version>
        <hibernate-types.version>2.21.1</hibernate-types.version>
        <testcontainers.version>1.19.2</testcontainers.version>
        <guava.version>30.0-jre</guava.version>
        <commons-collections.version>4.4</commons-collections.version>
        <jna.version>5.12.1</jna.version>
        <blaze-persistence.version>1.6.10</blaze-persistence.version>
        <sonar.java.binaries>.</sonar.java.binaries>
        <snakeyaml.version>2.2</snakeyaml.version>
        <jackson-bom.version>2.15.4</jackson-bom.version>
        <jackjson-databind.version>2.15.4</jackjson-databind.version>
        <terminus.cloud.storage.version>3.0.20250806.d13f5c1</terminus.cloud.storage.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-text.version>1.10.0</commons-text.version>
        <jooq.version>3.13.2</jooq.version>
        <google.auto.service.version>1.0.1</google.auto.service.version>
        <handlebars.version>4.3.1</handlebars.version>
        <fastjson.version>1.2.83</fastjson.version>
        <terminus.oplog.version>2.0.1.DEV-SNAPSHOT</terminus.oplog.version>
        <terminus.print.version>3.0.0.250630.0.RELEASE</terminus.print.version>
        <terminus.notice.version>2.0.0.250630.1.RELEASE</terminus.notice.version>
        <terminus.config.version>2.0.0.RELEASE</terminus.config.version>
        <!--mybatis-plus.version 版本需与 terminus-spring-boot-starter-mybatis 工程保持一致-->
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <langchain4j.version>0.35.0</langchain4j.version>
        <openai.version>3.0.2</openai.version>
        <mcp.version>0.10.0</mcp.version>
        <theokanning.version>0.18.2</theokanning.version>
        <jtokkit.version>1.0.0</jtokkit.version>
        <redisson.version>3.38.1</redisson.version>
        <terminus-common.version>2.0.0.RELEASE</terminus-common.version>
        <terminus-scheduler-common.version>2.0.1.RELEASE</terminus-scheduler-common.version>
        <terminus-i18n.version>2.0.0.RELEASE</terminus-i18n.version>
        <cloud.erda.monitor.version>1.0.8</cloud.erda.monitor.version>
        <gravity.version>2.1-SNAPSHOT</gravity.version>
        <sequence.version>2.0.0.RELEASE</sequence.version>
        <trantor-condition.version>1.0.2.RELEASE</trantor-condition.version>
        <trantor-org.version>1.0.2.RELEASE</trantor-org.version>
        <elasticsearch.version>7.17.6</elasticsearch.version>
        <aliyun-java-sdk-ons.version>3.1.7</aliyun-java-sdk-ons.version>
        <aliyun-ons20190214.version>1.0.0</aliyun-ons20190214.version>
        <aliyun-rocketmq20220801.version>1.0.1</aliyun-rocketmq20220801.version>
        <soa.version>2.0.1</soa.version>
        <joda.time.version>2.10.2</joda.time.version>
        <io.opentelemetry.version>1.37.0</io.opentelemetry.version>
        <graalvm.version>21.3.7</graalvm.version>
        <nashorn.version>15.6</nashorn.version>
        <json-schema.version>4.31.1</json-schema.version>
        <json-unit.version>2.38.0</json-unit.version>
        <reflections.version>0.10.2</reflections.version>
        <maven.version>3.8.5</maven.version>
        <maven-plugin-annotations.version>3.11.0</maven-plugin-annotations.version>
        <influxdb-client.version>6.12.0</influxdb-client.version>
        <terminus-util-version>2.0.0.250630.0.RELEASE</terminus-util-version>
        <apache.poi.version>5.2.5</apache.poi.version>
        <milvus.version>2.5.10</milvus.version>
        <protobuf-java.version>3.25.5</protobuf-java.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring-web.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.nacos</groupId>
                        <artifactId>nacos-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.jooq.trial-java-8</groupId>
                <artifactId>jooq</artifactId>
                <version>${jooq.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-driver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.auto.service</groupId>
                <artifactId>auto-service-annotations</artifactId>
                <version>${google.auto.service.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.common</groupId>
                <artifactId>terminus-common-api</artifactId>
                <version>${terminus-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.terminus.common</groupId>
                <artifactId>terminus-common-runtime</artifactId>
                <version>${terminus-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.terminus.common</groupId>
                <artifactId>terminus-spring-boot-starter-scheduler</artifactId>
                <version>${terminus-scheduler-common.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.i18n</groupId>
                <artifactId>terminus-i18n-spring-boot-starter</artifactId>
                <version>${terminus-i18n.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.i18n</groupId>
                <artifactId>terminus-i18n-api</artifactId>
                <version>${terminus-i18n.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.terminus.apidocs</groupId>
                        <artifactId>swagger2-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-ons</artifactId>
                <version>${aliyun-java-sdk-ons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>ons20190214</artifactId>
                <version>${aliyun-ons20190214.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>rocketmq20220801</artifactId>
                <version>${aliyun-rocketmq20220801.version}</version>
            </dependency>

            <!-- jackjson -->
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.time.version}</version>
            </dependency>
            <dependency>
                <!-- fallback, spring boot 3.x bom removed -->
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <!-- fallback, spring boot 3.x bom removed -->
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpmime.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-properties</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-lang</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-ide-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-ide-test</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-meta-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-meta-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-doc-engine-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-doc-engine-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-meta-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-meta-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-meta-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-meta-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-workflow-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-workflow-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-workflow-common-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-print-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-notice-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-notice-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-notice-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-notice-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-notice-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-code-engine</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-globalreq-engine</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-code-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-code-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-code-engine-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-code-engine-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-console-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-console-implement</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-console-management</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-engine</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-management</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-runtime</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-runtime-sdk</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-iam-adapter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>test-container</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-engine</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-engine-management</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-engine-runtime</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-scene-runtime-sdk</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-doc-engine-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-doc-engine-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-doc-engine-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-doc-engine-runtime-sdk</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-process-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-process-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-process-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-process-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-process-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-process-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-rule-engine-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-rule-engine-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-service-api-common</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-service-impl-common</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-service-dsl</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-service-management-api</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-service-management-impl</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-service-runtime-api</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-service-runtime-impl</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-service-business</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-service-engine-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-service-report</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-model-api-common</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-model-impl-common</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-condition-api</artifactId>
                <version>${trantor-condition.version}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-model-runtime-api</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-model-runtime-impl</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-model-runtime-sdk</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-model-management-api</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <artifactId>trantor-model-management-impl</artifactId>
                <groupId>io.terminus.trantor2</groupId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-nexus-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-nexus-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-application-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-application-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-application-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-application-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-console-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-console-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-runtime</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-runtime-sdk</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-datasource-engine-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-datasource-engine-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-datasource-engine-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-datasource-engine-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-datasource-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-search-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-search-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-search-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-search-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-search-es-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-search-mq-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-search-mq-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-dump-server</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup</groupId>
                <artifactId>javapoet</artifactId>
                <version>${javapoet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.auto.service</groupId>
                <artifactId>auto-service</artifactId>
                <version>${auto-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.vertical-blank</groupId>
                <artifactId>sql-formatter</artifactId>
                <version>${sql-formatter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger3.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.iam</groupId>
                <artifactId>iam-app</artifactId>
                <version>${iam.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.iam</groupId>
                <artifactId>iam-sdk</artifactId>
                <version>${iam.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.iam</groupId>
                <artifactId>iam-internal-sdk</artifactId>
                <version>${iam.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.iam</groupId>
                <artifactId>iam-spring-boot-starter</artifactId>
                <version>${iam.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-http</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <!-- provided scope 仅用于 compilation & test classpath，不可传递依赖 -->
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger3.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.vladmihalcea</groupId>
                <artifactId>hibernate-types-60</artifactId>
                <version>${hibernate-types.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor.workflow</groupId>
                <artifactId>workflow-common</artifactId>
                <version>${trantor.workflow.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor.workflow</groupId>
                <artifactId>workflow-api</artifactId>
                <version>${trantor.workflow.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor.workflow</groupId>
                <artifactId>workflow-sdk</artifactId>
                <version>${trantor.workflow.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor.workflow</groupId>
                <artifactId>workflow-api</artifactId>
                <version>${trantor.workflow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.common</groupId>
                <artifactId>terminus-spring-boot-starter-rocketmq</artifactId>
                <version>${terminus-rocketmq}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>${janino.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>commons-compiler-jdk</artifactId>
                <version>${janino.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc</artifactId>
                <version>${sharding-jdbc.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-test-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>
            <dependency>
                <groupId>com.blazebit</groupId>
                <artifactId>blaze-persistence-integration-hibernate-6.2</artifactId>
                <version>${blaze-persistence.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.blazebit</groupId>
                <artifactId>blaze-persistence-core-impl-jakarta</artifactId>
                <version>${blaze-persistence.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.blazebit</groupId>
                <artifactId>blaze-persistence-core-api-jakarta</artifactId>
                <version>${blaze-persistence.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jettison</groupId>
                <artifactId>jettison</artifactId>
                <version>1.5.2</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.cloud</groupId>
                <artifactId>cloud-storage-core</artifactId>
                <version>${terminus.cloud.storage.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.jettison</groupId>
                        <artifactId>jettison</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.jknack</groupId>
                <artifactId>handlebars</artifactId>
                <version>${handlebars.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <artifactId>operation-log-api</artifactId>
                <groupId>io.terminus</groupId>
                <version>${terminus.oplog.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus</groupId>
                <artifactId>operation-log-spring-boot-starter</artifactId>
                <version>${terminus.oplog.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-module-engine-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-module-engine-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-module-engine-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-module-engine-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-module-engine-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-rule-engine-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-module-engine-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>state-engine-manage</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-connector-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-connector-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-connector-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-connector-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-connector-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-connector-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-api-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-impl-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-management</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-runtime</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-center-runtime</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-management-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-management-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-center-runtime-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-trigger-center-runtime-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-unify-runtime</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-unify-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-unify-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>terminus-notice-sdk</artifactId>
                <version>${terminus.notice.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <!-- OpenAI -->
            <dependency>
                <groupId>com.openai</groupId>
                <artifactId>openai-java</artifactId>
                <version>${openai.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-open-ai</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.theokanning.openai-gpt3-java</groupId>
                <artifactId>service</artifactId>
                <version>${theokanning.version}</version>
            </dependency>
            <dependency>
                <groupId>com.knuddels</groupId>
                <artifactId>jtokkit</artifactId>
                <version>${jtokkit.version}</version>
            </dependency>
            <dependency>
                <groupId>io.modelcontextprotocol.sdk</groupId>
                <artifactId>mcp</artifactId>
                <version>${mcp.version}</version>
            </dependency>
            <!-- erda -->
            <dependency>
                <groupId>cloud.erda</groupId>
                <artifactId>msp-monitor-sdk</artifactId>
                <version>${cloud.erda.monitor.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.common</groupId>
                <artifactId>sequence-center-api</artifactId>
                <version>${sequence.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.terminus.common</groupId>
                <artifactId>terminus-spring-boot-starter-sequence</artifactId>
                <version>${sequence.version}</version>
            </dependency>
            <dependency>
                <groupId>com.predic8</groupId>
                <artifactId>soa-model-core</artifactId>
                <version>${soa.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-bom</artifactId>
                <version>${io.opentelemetry.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-sdk-extension-autoconfigure</artifactId>
                <version>${io.opentelemetry.version}</version>
            </dependency>
            <dependency>
                <groupId>org.graalvm.js</groupId>
                <artifactId>js</artifactId>
                <version>${graalvm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.nashorn</groupId>
                <artifactId>nashorn-core</artifactId>
                <version>${nashorn.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.victools</groupId>
                <artifactId>jsonschema-generator</artifactId>
                <version>${json-schema.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.victools</groupId>
                <artifactId>jsonschema-module-jackson</artifactId>
                <version>${json-schema.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-plugin-api</artifactId>
                <version>${maven.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-core</artifactId>
                <version>${maven.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven.plugin-tools</groupId>
                <artifactId>maven-plugin-annotations</artifactId>
                <version>${maven-plugin-annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-dors-common-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-dors-common-impl</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-dors-runtime</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-dors-console</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>net.javacrumbs.json-unit</groupId>
                <artifactId>json-unit</artifactId>
                <version>${json-unit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.influxdb</groupId>
                <artifactId>influxdb-client-java</artifactId>
                <version>${influxdb-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.tool</groupId>
                <artifactId>terminus-poi-util</artifactId>
                <version>${terminus-util-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-full</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>io.milvus</groupId>
                <artifactId>milvus-sdk-java</artifactId>
                <version>${milvus.version}</version>
            </dependency>
            <!-- 显式声明 protobuf-java 版本解决因包版本冲突问题导致 milvus 间接依赖的 protobuf-java 版本没生效 -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>
            <!-- spring-cloud-alibaba-dependencies是以import的方式声明的，
            并且里面声明了 rocketmq的client和acl是5.1.4和terminus-spring-boot-starter-rocketmq冲突了
             -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-acl</artifactId>
                <version>4.9.8</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>4.9.8</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- refer https://maven.apache.org/plugins/maven-checkstyle-plugin/usage.html -->
    <!--    <reporting>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.apache.maven.plugins</groupId>-->
    <!--                <artifactId>maven-checkstyle-plugin</artifactId>-->
    <!--                <reportSets>-->
    <!--                    <reportSet>-->
    <!--                        <reports>-->
    <!--                            <report>checkstyle</report>-->
    <!--                        </reports>-->
    <!--                    </reportSet>-->
    <!--                </reportSets>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--    </reporting>-->
    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.objenesis</groupId>
                    <artifactId>objenesis</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <distributionManagement>
        <repository>
            <id>terminus</id>
            <name>terminus release repository</name>
            <url>${nexus.release.url}</url>
        </repository>
        <snapshotRepository>
            <id>terminus</id>
            <name>terminus snapshot repository</name>
            <url>${nexus.snapshot.url}</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-plugin-plugin</artifactId>
                    <version>3.11.0</version>
                </plugin>
                <plugin>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-maven-plugin</artifactId>
                    <version>${revision}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/test/java</directory>
                <includes>
                    <include>**/*.json</include>
                    <include>**/*.txt</include>
                </includes>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>setup-git-hooks</id>
                        <phase>initialize</phase>
                        <configuration>
                            <skip>${skipGitHooks}</skip>
                            <target>
                                <copy todir="${basedir}/.git/hooks" overwrite="true">
                                    <fileset dir="${basedir}/scripts/gitHooks" includes="*"/>
                                </copy>
                                <chmod dir="${basedir}/.git/hooks" perm="777" includes="**/*"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <inherited>false</inherited>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArguments>
                        <bootclasspath>
                            ${java.home}${file.separator}lib${file.separator}rt.jar${path.separator}${java.home}${file.separator}lib${file.separator}jce.jar${path.separator}${java.home}${file.separator}..${file.separator}lib${file.separator}tools.jar
                        </bootclasspath>
                    </compilerArguments>
                    <compilerArgs>
                        <arg>-Xpkginfo:always</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.28</version>
                        </path>
                        <path>
                            <groupId>org.hibernate.orm</groupId>
                            <artifactId>hibernate-jpamodelgen</artifactId>
                            <version>${hibernate.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>com.google.auto.service</groupId>
                            <artifactId>auto-service</artifactId>
                            <version>${auto-service.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.1.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-checkstyle-plugin</artifactId>-->
            <!--                <version>${checkstyle.plugin.version}</version>-->
            <!--                <configuration>-->
            <!--&lt;!&ndash;                    <configLocation>trantor_checks.xml</configLocation>&ndash;&gt;-->
            <!--                    <encoding>UTF-8</encoding>-->
            <!--                    <consoleOutput>true</consoleOutput>-->
            <!--                    <failsOnError>false</failsOnError>-->
            <!--                    <linkXRef>false</linkXRef>-->
            <!--                </configuration>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>validate</id>-->
            <!--                        <phase>validate</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>check</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.7.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-test-resources</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <encoding>UTF-8</encoding>
                            <outputDirectory>${project.build.directory}/test-classes</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/test/java</directory>
                                    <includes>
                                        <include>**/*.json</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <directory>src/test/resources</directory>
                                    <includes>
                                        <include>**/*.json</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.6</version>
                <executions>
                    <!--first execution : for preparing JaCoCo runtime agent-->
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <!--second execution : for creating code coverage reports-->
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>templating-maven-plugin</artifactId>
                <version>1.0.0</version>
                <executions>
                    <execution>
                        <id>filter-src</id>
                        <goals>
                            <goal>filter-sources</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
