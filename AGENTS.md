# Repository Guidelines

## 沟通与语言
- 默认使用中文进行交流与回复（包括代码评审说明、变更说明、提交信息描述等）；如需英文请在需求中明确说明。

## 项目结构与模块组织
- 根模块使用 Maven 多模块（Java 17，Spring Boot 3）。核心目录：`trantor-engines/`、`trantor-console/`、`trantor-runtime/`、`trantor-meta/`、`trantor-lang/`、`trantor-ide/`、`trantor-common/`、`trantor-iam-adapter/`、`trantor-maven-plugin/`、`trantor-dors/`、`trantor-test-tools/`、`trantor-unify-runtime/`。
- 引擎子模块命名模式：`*-api-common/`（公共接口）、`*-impl-common/`（实现共享）、`*-management-*`（控制台管理）、`*-runtime-*`（运行态）。
- 测试与数据：各模块 `src/test/java`；公共测试数据放在根目录 `testData/`。

## 构建、测试与本地运行
- 注意：由于部分目录可能为 git subtree，`.git` 可能不是目录，请在所有 Maven 命令中附加 `-DskipGitHooks=true` 以跳过 Git hooks 初始化。
- 清理与编译：`mvn clean compile -DskipGitHooks=true`
- 全量打包：`mvn clean package -DskipGitHooks=true`
- 跳测快速构建：`mvn compile -Dmaven.test.skip -DstyleCheck.skip -T4 -DskipGitHooks=true`
- 安装到本地仓库：`mvn clean install -DskipGitHooks=true`

## 代码风格与命名
- 遵循 `.editorconfig` 与 `trantor_checks.xml`（Checkstyle）。字段注释使用多行 JavaDoc 规范。
- 包名小写连词；类名 `PascalCase`，方法/变量 `camelCase`；模块/工件命名沿用 `trantor-<域>-<角色>` 模式。

## 测试规范
- 框架：JUnit 5、Spring Boot Test、Testcontainers；覆盖率建议启用 JaCoCo。
- 用例命名：`*Test.java`，包路径与被测代码镜像；示例：`mvn test -DskipGitHooks=true`，或模块内测试：`mvn -T4 -pl :module-name -am test -DskipGitHooks=true`。
- 数据迁移：默认使用 Liquibase（`src/main/resources/db/changelog/`）。

## 提交与合并请求
- 提交规范（见 `.gitmessage`）：`<type>(<scope>): <摘要>`，如：`feat(scene): 新增供应链场景`。type 示例：feat/fix/test/docs/perf/chore；scope 示例：model/scene/flow/connector/console/common。
- PR 要求：清晰描述与动机，关联任务/Issue（含 erda taskId），涉及 UI/控制台改动附截图；通过 CI；同步更新 `docs/`（如需要）。

## 配置与安全
- 环境与配置：`dev/test/prod` Profile；常用环境文件：`.env-runtime`、`.env-console`；敏感信息用环境变量注入。
- 权限与合规：依赖 IAM 适配与权限引擎；引入依赖请关注安全告警与许可条款。

## 开发备忘（请务必遵循）
- 至少本地编译通过再宣称完成；可仅编译相关模块。为提速可采用“跳测快速构建”（不 clean、跳过测试）。
- REST 控制器统一返回 Response 类，保持返回结构一致性。
- 严格遵循 `.editorconfig` 和 Checkstyle；提交前自检格式与导包顺序。
- 文档与实现保持同步：涉及行为或接口变更时同步更新 `docs/` 与相应模块 README/注释。
