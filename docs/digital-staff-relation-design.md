# 数字员工关系元数据设计文档

## 概述

本文档描述了数字员工关系元数据的设计，用于表达和维护数字员工之间的工作关系。该设计支持六种主要的关系类型，并提供了灵活的配置机制来满足不同的业务场景需求。

## 核心组件

### 1. 数字员工关系元数据 (AIDigitalStaffRelationMeta)

主要的元数据类，继承自 `ModuleBaseMeta`，用于定义数字员工之间的关系。

**主要属性：**
- `relationType`: 关系类型（枚举）
- `direction`: 关系方向（单向/双向）
- `sourceStaffIds`: 源数字员工ID列表
- `targetStaffIds`: 目标数字员工ID列表
- `relationConfig`: 关系配置参数
- `isEnabled`: 是否启用
- `priority`: 优先级

### 2. 工作场景元数据 (WorkSceneMeta)

用于管理包含多个数字员工及其关系的工作场景。

**主要属性：**
- `sceneType`: 场景类型
- `status`: 场景状态
- `participantStaffIds`: 参与的数字员工列表
- `relationIds`: 关系ID列表
- `sceneConfig`: 场景配置

## 关系类型

### 1. 独立完成 (INDEPENDENT)
- **描述**: 数字员工独立完成任务，无需与其他员工协作
- **特点**: 单一员工，自包含的工作流程
- **应用场景**: 数据分析、报告生成、简单决策等

### 2. 交接 (HANDOVER)
- **描述**: 一个数字员工完成任务后将结果交接给另一个数字员工
- **特点**: 单向关系，有明确的交接流程
- **配置参数**:
  - `triggerCondition`: 交接触发条件
  - `dataTypes`: 交接数据类型
  - `requireConfirmation`: 是否需要确认
  - `timeoutMinutes`: 超时时间
  - `retryCount`: 重试次数

### 3. 汇报 (REPORT)
- **描述**: 下级数字员工向上级数字员工汇报工作进展或结果
- **特点**: 单向关系，定期或实时汇报
- **配置参数**:
  - `reportFrequency`: 汇报频率（cron表达式）
  - `reportTemplate`: 汇报内容模板
  - `dataScope`: 汇报数据范围
  - `realTimeReport`: 是否实时汇报
  - `reportFormat`: 汇报格式

### 4. 协商 (CONSULTATION)
- **描述**: 多个数字员工之间进行协商讨论以达成共识
- **特点**: 双向关系，多方参与
- **配置参数**:
  - `consultationTopic`: 协商主题
  - `consultationRules`: 协商规则
  - `decisionAlgorithm`: 决策算法
  - `maxRounds`: 最大协商轮次
  - `timeoutMinutes`: 协商超时时间
  - `requireHumanIntervention`: 是否需要人工介入

### 5. 交接/汇报 (HANDOVER_REPORT)
- **描述**: 既有交接又有汇报的复合关系
- **特点**: 结合了交接和汇报的特性
- **配置参数**: 包含交接和汇报的所有配置参数

### 6. 汇集 (AGGREGATION)
- **描述**: 多个数字员工的工作结果汇集到一个数字员工处进行整合
- **特点**: 多对一关系，数据整合
- **配置参数**:
  - `aggregationStrategy`: 汇集策略
  - `mergeRules`: 数据合并规则
  - `conflictResolutionStrategy`: 冲突解决策略
  - `triggerCondition`: 汇集触发条件
  - `maxWaitMinutes`: 最大等待时间
  - `minParticipants`: 最小参与者数量

## 工作场景类型

### 1. 简单场景 (SIMPLE)
- 单个数字员工独立完成的场景

### 2. 流水线场景 (PIPELINE)
- 多个数字员工按顺序协作的场景

### 3. 协作场景 (COLLABORATIVE)
- 多个数字员工并行协作的场景

### 4. 层级场景 (HIERARCHICAL)
- 具有上下级关系的数字员工协作场景

### 5. 混合场景 (HYBRID)
- 包含多种关系类型的复杂场景

### 6. 自定义场景 (CUSTOM)
- 用户自定义的特殊场景

## 服务接口

### 1. DigitalStaffRelationService
提供数字员工关系的CRUD操作和高级功能：
- 关系的创建、更新、删除、查询
- 关系图构建和路径查找
- 关系验证和批量操作

### 2. WorkSceneService
提供工作场景的管理功能：
- 场景的创建、更新、删除、查询
- 场景的启动、停止、暂停、恢复
- 场景执行状态监控和历史记录
- 场景配置的导入导出

## 工具类

### DigitalStaffRelationBuilder
提供便捷的方法来创建各种类型的数字员工关系：
- `createIndependentRelation()`: 创建独立完成关系
- `createHandoverRelation()`: 创建交接关系
- `createReportRelation()`: 创建汇报关系
- `createConsultationRelation()`: 创建协商关系
- `createAggregationRelation()`: 创建汇集关系
- `createHandoverReportRelation()`: 创建复合关系

## 使用示例

### 创建销售团队协作场景

```java
// 1. 创建数字员工关系
// 销售订价分析助手 -> 销售主管 (汇报关系)
AIDigitalStaffRelationMeta reportRelation = DigitalStaffRelationBuilder.createReportRelation(
    "sales_pricing_analyst", 
    "sales_manager", 
    "0 0 9 * * ?", // 每天9点汇报
    false
);

// 销售订价分析助手 <-> 销售订价分析助手 (协商关系)
AIDigitalStaffRelationMeta consultationRelation = DigitalStaffRelationBuilder.createConsultationRelation(
    Arrays.asList("sales_pricing_analyst_1", "sales_pricing_analyst_2"),
    "定价策略协商",
    5
);

// 2. 创建工作场景
WorkSceneMeta workScene = new WorkSceneMeta();
WorkSceneMeta.Props sceneProps = new WorkSceneMeta.Props();
sceneProps.setSceneType(WorkSceneType.HIERARCHICAL);
sceneProps.setStatus(WorkSceneStatus.READY);
sceneProps.setParticipantStaffIds(Arrays.asList(
    "sales_manager", 
    "sales_pricing_analyst_1", 
    "sales_pricing_analyst_2"
));
sceneProps.setIsEnabled(true);

workScene.setResourceProps(sceneProps);
workScene.setName("销售团队协作场景");
workScene.setDescription("销售主管与定价分析助手的协作场景");
```

## 扩展性

该设计具有良好的扩展性：

1. **新关系类型**: 可以通过扩展 `DigitalStaffRelationType` 枚举添加新的关系类型
2. **自定义配置**: 通过 `customParams` 字段支持自定义配置参数
3. **插件化**: 服务接口支持插件化扩展
4. **多租户**: 继承自 `ModuleBaseMeta`，天然支持多租户

## 总结

该数字员工关系元数据设计提供了：
- 完整的关系类型覆盖
- 灵活的配置机制
- 强大的管理功能
- 良好的扩展性
- 便捷的使用工具

能够满足各种复杂的数字员工协作场景需求。
