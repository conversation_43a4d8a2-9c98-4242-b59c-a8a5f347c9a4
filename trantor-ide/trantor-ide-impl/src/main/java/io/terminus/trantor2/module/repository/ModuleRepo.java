package io.terminus.trantor2.module.repository;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.repository.ResourceRepository;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.meta.ModuleType;
import io.terminus.trantor2.module.model.query.ModuleRelationQueryMode;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2023/7/10 5:42 PM
 */
@Repository
public interface ModuleRepo extends ResourceRepository<ModuleMeta> {

    default List<String> findRelatedModuleKeys(ModuleMeta module, ModuleRelationQueryMode mode, ResourceContext ctx) {
        if (Objects.equals(ModuleRelationQueryMode.CURRENT, mode)) {
            return new ArrayList<>();
        }
        // TODO: check Portal should be moved out
        if (Objects.equals(module.getResourceProps().getType(), ModuleType.Portal)
                || Objects.equals(ModuleRelationQueryMode.ALL, mode)) {
            return findAll(Field.type().equal(MetaType.Module.name()), ctx).stream()
                    .filter(item -> item.getResourceProps().getType() == ModuleType.Module)
                    .map(BaseMeta::getKey)
                    .filter(key -> !Objects.equals(key, module.getKey()))
                    .collect(Collectors.toList());
        } else {
            return module.getResourceProps().getRelatedModuleKeys();
        }
    }

    default List<ModuleMeta> findAllPortalByTeam(ResourceContext ctx) {
        return findAll(Field.type().equal(MetaType.Module.name()), ctx).stream()
                .filter(item -> Objects.equals(item.getResourceProps().getType(), ModuleType.Portal))
                .collect(Collectors.toList());
    }

    /**
     * 查询所有内部模块
     * 返回一个包含所有内部模块key，并且大小写不敏感的集合列表
     *
     * @return Collection 内部模块标识集合
     */
    default Collection<String> findAllNativeModuleKeys() {
        List<ModuleMeta> moduleMetas = findAll(Cond.all(), ResourceContext.ctxFromThreadLocal());
        Set<String> nativeModuleKeys = new TreeSet<>(String.CASE_INSENSITIVE_ORDER);
        moduleMetas.stream().filter(ModuleMeta::isNativeModule).map(BaseMeta::getKey).forEach(nativeModuleKeys::add);
        return nativeModuleKeys;
    }
}
