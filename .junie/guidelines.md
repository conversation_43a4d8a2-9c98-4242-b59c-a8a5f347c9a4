# Project Guidelines for Trantor2

This document provides a concise project overview and practical guidance for <PERSON><PERSON> when making changes to this repository.

## 1. Project Overview
- Technology: Java, Maven (multi-module).
- Entry: Root `pom.xml` aggregates multiple submodules.
- Common modules at the root (non-exhaustive):
  - trantor-common, trantor-console, trantor-engines, trantor-ide, trantor-meta, trantor-runtime, trantor-unify-runtime, trantor-lang, trantor-impl-common, trantor-maven-plugin, trantor-test-tools
- Tooling and helpers:
  - `mvnw`, `mvnw.cmd` for consistent Maven wrapper usage.
  - `lombok.config` indicates Lombok is used; prefer Lombok idioms when touching POJOs.
  - Docker files and CI configs are present but not typically needed for local verification.
  - Logs and temporary artifacts (e.g., `logs/`, `target/`) should be ignored/unchanged.

## 2. When Junie Should Run Tests
- Run unit tests when:
  - Changing Java/Kotlin business logic or behavior.
  - Modifying public APIs or interfaces.
  - Adjusting serialization/manifest or zip utilities (e.g., changes under `trantor-meta`, `trantor-ide`, or `trantor-engines`).
- Skipping tests is acceptable for non-code edits (docs, configuration comments) if the change cannot affect build/test outcomes.

## 3. How to Run Tests (Maven)
Use Maven Wrapper from the repository root unless a module-specific context is clearer.
- Run all tests in all modules:
  - ./mvnw -q -DtrimStackTrace=false test
- Run tests for a specific module (plus its dependencies):
  - ./mvnw -q -pl <module-path> -am -DtrimStackTrace=false test
  - Example: ./mvnw -q -pl trantor-meta -am test
- Run a single test class or one method:
  - ./mvnw -q -Dtest=MyTestClass test
  - ./mvnw -q -Dtest=MyTestClass#shouldDoX test

Notes:
- Use -q to keep logs concise during automated runs; drop -q locally if you need verbose output.
- If build time is long, prefer targeted module testing via -pl and -am.

## 4. Build Instructions
- Full build (without running tests):
  - ./mvnw -T 1C -DskipTests -U install
- Full build (with tests):
  - ./mvnw -T 1C -U install
- Build one module (and required dependencies), skipping tests:
  - ./mvnw -pl <module-path> -am -DskipTests install

When to build before submitting:
- If you changed any Java code, always run at least a module build to catch compilation errors.
- For documentation-only changes, building is optional.

## 5. Coding and Contribution Style
- Minimal, targeted changes: Prefer the smallest code delta that fully addresses the issue.
- Follow existing patterns, package structure, and naming conventions in the target module.
- Lombok: Do not manually add boilerplate where Lombok annotations are already used.
- Null-safety and error handling: Be defensive around IO, network, and file operations (e.g., zip/manifest utilities).
- API stability: Avoid changing public method signatures unless required; if changed, update usages and tests accordingly.
- Logging: Keep logs consistent; avoid noisy debug logs in production paths.

## 6. Repo Hygiene
- Do not commit generated content under `target/` or any local logs under `logs/`.
- Keep changes within the project directory; do not create files outside the repository root.
- Respect existing license headers if present in files you modify.

## 7. How Junie Should Work in This Repo
- Always include the <UPDATE> block with plan/progress in each response.
- Prefer editing only the necessary files; avoid broad refactors.
- Before submitting, if code was changed:
  - Run targeted tests/builds per sections 3–4.
  - Verify no compilation errors and relevant tests pass.
- If only documentation/config is edited, no build/test run is required.

## 8. Quick References
- Root build (tests): ./mvnw -T 1C -U install
- Root tests only: ./mvnw -q test
- Module tests: ./mvnw -q -pl <module> -am test
- Single test: ./mvnw -q -Dtest=ClassName[#method] test

Last updated: 2025-08-13
